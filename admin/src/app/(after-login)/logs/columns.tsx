"use client";

import { Column, createColumnHelper } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { HistoryItem } from "@/adapter/dynamodb/history";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { logLabel } from "@/lib/log-label";

dayjs.extend(utc);

export type HistoryItemWithUserName = HistoryItem & { UserName: string };

const columnHelper = createColumnHelper<HistoryItemWithUserName>();

export const columns = [
  columnHelper.accessor("UserName", {
    id: "userName",
    header: ({ column }) => <SortHeader label="名前" column={column} />,
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("ID", {
    id: "timestamp",
    header: ({ column }) => <SortHeader label="日時" column={column} />,
    cell: (info) => {
      const timestamp = info.getValue();
      return dayjs(timestamp).local().format("YYYY/MM/DD HH:mm:ss");
    },
  }),
  columnHelper.accessor("Data.kind", {
    id: "kind",
    header: ({ column }) => <SortHeader label="操作" column={column} />,
    cell: (info) => {
      const kind = info.getValue();
      const label = logLabel(kind);
      return label;
    },
  }),
  columnHelper.display({
    id: "text",
    cell: (info) => {
      const data = info.row.original.Data;
      const kind = data.kind;
      let text;
      switch (kind) {
        case "program#create":
        case "program#update-name":
        case "program#update-expires-at":
        case "program#delete":
          text = `番組ID: ${data.programId}`;
          break;
        case "program#create-message-topic":
        case "program#delete-message-topic":
          text = `番組ID: ${data.programId}, メッセージトピックID: ${data.messageTopicId}`;
          break;
        case "program-scale#update":
          text = `番組ID: ${data.programId}, シリーズID: ${data.seriesId || "なし"}`;
          break;
        case "scale#create":
        case "scale#update":
        case "scale#delete":
          text = `スケールID: ${data.scaleId}`;
          break;
        case "scale-schedule#create":
        case "scale-schedule#update":
        case "scale-schedule#delete":
          text = `スケジュールID: ${data.scaleScheduleId}`;
          break;
        case "admin#switch-user-role":
          text = `ユーザーID: ${data.userId}, ${data.to === "admin" ? "管理者" : "一般ユーザー"}に変更`;
          break;
        case "admin#update-user-note":
        case "admin#delete-user":
          text = `ユーザーID: ${data.userId}`;
          break;
        default:
          text = null;
          break;
      }
      return text;
    },
  }),
];

function SortHeader<T>({
  label,
  column,
}: {
  label: string;
  column: Column<HistoryItemWithUserName, T>;
}) {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {label}
      <ArrowUpDown className="ml-2 h-4 w-4" />
    </Button>
  );
}
