"use client";

import { HistoryItem } from "@/adapter/dynamodb/history";
import { LogTable } from "./log-table";
import { buttonVariants, Button } from "@/components/ui/button";
import Link from "next/link";
import { User } from "@/adapter/dynamodb/user";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { DatePicker } from "@/components/date-picker";
import dayjs from "dayjs";

export function LogView(props: {
  history: {
    items: HistoryItem[];
    lastKey?: string;
  };
  users: User[];
}) {
  const itemsWithUsername = props.history.items.map((item) => {
    const user = props.users.find((user) => user.ID === item.UserId);
    return {
      ...item,
      UserName: user?.CustomClaims?.nhkName ?? item.UserId,
    };
  });

  return (
    <div>
      <div className="flex justify-between py-4">
        <div className="flex gap-2">
          <DateSelect />
          <UserSelect users={props.users} />
        </div>
        <NextPageButton lastKey={props.history.lastKey} />
      </div>
      <LogTable data={itemsWithUsername} />
      <div className="flex justify-end py-4">
        <NextPageButton lastKey={props.history.lastKey} />
      </div>
    </div>
  );
}

function DateSelect() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const date = searchParams.get("date");

  function updateDate(date: Date | undefined) {
    const params = new URLSearchParams(searchParams.toString());
    if (date) {
      params.set("date", dayjs(date).format("YYYY-MM-DD"));
    } else {
      params.delete("date");
    }
    router.push(`/logs?${params.toString()}`);
  }

  return (
    <DatePicker
      date={date ? dayjs(date).toDate() : undefined}
      onSelect={updateDate}
      showClearButton
    />
  );
}

function UserSelect(props: { users: User[] }) {
  const [value, setValue] = useState("all");
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const userId = searchParams.get("userId") ?? "all";
    setValue(userId);
  }, [searchParams]);

  function updateQueryParams(userId: string) {
    const params = new URLSearchParams(searchParams.toString());
    if (userId === "all") {
      params.delete("userId");
    } else {
      params.set("userId", userId);
    }
    router.push(`/logs?${params.toString()}`);
  }

  return (
    <Select onValueChange={updateQueryParams} value={value}>
      <SelectTrigger className="w-64">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="all">ユーザー名</SelectItem>
          {props.users.map((user) => (
            <SelectItem key={user.ID} value={user.ID}>
              {user.CustomClaims?.nhkName ?? user.ID}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}

function NextPageButton(props: { lastKey?: string }) {
  const searchParams = useSearchParams();
  if (props.lastKey == null) {
    return <Button disabled>次のページ</Button>;
  }

  const params = new URLSearchParams(searchParams.toString());
  params.set("from", encodeURIComponent(props.lastKey));

  return (
    <Link href={`/logs?${params.toString()}`} className={buttonVariants()}>
      次のページ
    </Link>
  );
}
