import { fetchHistory, fetchUserHistory } from "@/adapter/dynamodb/history";
import { PageTitle } from "@/components/page-title";
import { getSearchParams, SearchParams } from "@/utils/search-params";
import { LogView } from "./log-view";
import { fetchUsers } from "@/adapter/dynamodb/user";
import { getLoginUser } from "@/auth";
import { Forbidden } from "@/components/forbidden";

export default async function LogPage(props: { searchParams: SearchParams }) {
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    return <Forbidden />;
  }

  const params = await props.searchParams;
  const from = getSearchParams(params, "from");
  const userId = getSearchParams(params, "userId");
  const date = getSearchParams(params, "date");

  const [history, users] = await Promise.all([
    userId
      ? fetchUserHistory(userId, from && decodeURIComponent(from), date)
      : fetchHistory(from && decodeURIComponent(from), date),
    fetchUsers(),
  ]);

  return (
    <div>
      <PageTitle>操作履歴</PageTitle>
      <LogView history={history} users={users} />
    </div>
  );
}
