import { fetchAggAPIAccessTokens } from "@/adapter/dynamodb/agg-api-access-token";
import { getLoginUser } from "@/auth";
import { PageTitle } from "@/components/page-title";
import { CreateAggAPIAccessTokenButton } from "@/components/agg-api/create-agg-api-access-token-button";
import { fetchPrograms } from "@/adapter/dynamodb/program";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { InfoIcon, LinkIcon } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getTokenProgramId } from "@/adapter/dynamodb/util-client";
import { TokenDetailButton } from "@/components/agg-api/token-detail-button";
import Link from "next/link";

dayjs.extend(utc);
dayjs.extend(timezone);

export default async function AggAPIPermissionsPage() {
  const { loginUser, tokens, programs } = await fetchTokensByUser();

  return (
    <div className="space-y-4">
      <PageTitle>集計APIアクセス管理</PageTitle>
      <div className="flex items-center gap-4 rounded border p-4">
        <InfoIcon className="h-4 w-4" />
        <p>トークンの変更が集計APIに反映されるまで1時間程度かかります</p>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>名前</TableHead>
              <TableHead>対象番組</TableHead>
              <TableHead>有効期限</TableHead>
              <TableHead></TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tokens.map((token) => {
              const tokenProgramId = getTokenProgramId(token);
              const program = tokenProgramId
                ? (programs.find((p) => p.ID === tokenProgramId) ?? null)
                : null;
              const expiresAt = token.Data.expiresAt
                ? dayjs(token.Data.expiresAt)
                : null;
              // 期限の1ヶ月前から表示
              const isExpired =
                expiresAt && expiresAt.isBefore(dayjs().add(1, "month"));

              return (
                <TableRow key={token.ID}>
                  <TableCell>{token.Data.name}</TableCell>
                  <TableCell>
                    {program?.Data.name ?? "なし（全番組アクセス可能）"}
                  </TableCell>
                  <TableCell
                    className={isExpired ? "font-semibold text-red-500" : ""}
                  >
                    {token.Data.expiresAt
                      ? dayjs(token.Data.expiresAt).format("YYYY/MM/DD")
                      : ""}
                  </TableCell>
                  <TableCell>
                    <TokenDetailButton token={token} program={program} />
                  </TableCell>
                  <TableCell>
                    {program ? (
                      <Link
                        href={`/programs/${program.ID}`}
                        className="flex items-center gap-2"
                      >
                        番組詳細
                        <LinkIcon className="h-4 w-4" />
                      </Link>
                    ) : null}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
      {tokens.length === 0 && (
        <p className="text-sm opacity-60">
          アクセス権が作成されていないか、閲覧権限がありません
        </p>
      )}
      {loginUser.isAdmin && (
        <div className="flex justify-end">
          <CreateAggAPIAccessTokenButton
            isAdmin={loginUser.isAdmin}
            programs={programs}
          />
        </div>
      )}
    </div>
  );
}

async function fetchTokensByUser() {
  let [loginUser, tokens, programs] = await Promise.all([
    getLoginUser(),
    fetchAggAPIAccessTokens(),
    fetchPrograms(),
  ]);

  if (loginUser.isAdmin) {
    return { loginUser, tokens, programs };
  }

  tokens = tokens.filter((token) => {
    const tokenProgramId = getTokenProgramId(token);
    return loginUser.permissions.some(
      (permission) => tokenProgramId === permission.ID,
    );
  });
  programs = programs.filter((program) =>
    loginUser.permissions.some((permission) => program.ID === permission.ID),
  );

  return { loginUser, tokens, programs };
}
