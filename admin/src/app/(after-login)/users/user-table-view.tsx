"use client";

import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useEffect, useState } from "react";
import { User } from "@/adapter/dynamodb/user";
import { columns } from "./columns";
import { useRouter, useSearchParams } from "next/navigation";
import { Button, buttonVariants } from "@/components/ui/button";
import Link from "next/link";
import { useDebounce } from "@/utils/hooks";

export function UserTableView({ data, lastKey }: DataTableProps) {
  return (
    <div>
      <div className="flex justify-between py-4">
        <SearchInput />
        <NextPageButton lastKey={lastKey} />
      </div>
      <UserTable data={data} />
      <div className="flex justify-end py-4">
        <NextPageButton lastKey={lastKey} />
      </div>
    </div>
  );
}

// https://ui.shadcn.com/docs/components/data-table

interface DataTableProps {
  data: User[];
  lastKey?: string;
}

function UserTable({ data }: DataTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && "selected"}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

function SearchInput() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const search = searchParams.get("search") ?? "";
  const [searchText, setSearchText] = useState(search);
  const debouncedValue = useDebounce(searchText, 500);

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("search", debouncedValue.trim());
    router.push(`/users?${params.toString()}`);
  }, [debouncedValue, searchParams, router]);

  return (
    <input
      type="search"
      placeholder="検索"
      className="w-64 rounded-md border p-2"
      value={searchText}
      onChange={(e) => setSearchText(e.target.value)}
    />
  );
}

function NextPageButton(props: { lastKey?: string }) {
  const searchParams = useSearchParams();
  if (props.lastKey == null) {
    return <Button disabled>次のページ</Button>;
  }

  const params = new URLSearchParams(searchParams.toString());
  params.set("from", encodeURIComponent(props.lastKey));

  return (
    <Link href={`/users?${params.toString()}`} className={buttonVariants()}>
      次のページ
    </Link>
  );
}
