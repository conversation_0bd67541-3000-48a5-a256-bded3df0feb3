import { PageTitle } from "@/components/page-title";
import { UserInfo } from "./user";
import { fetchUser } from "@/adapter/dynamodb/user";
import { fetchProgramPermissionsByUserId } from "@/adapter/dynamodb/program-permission";
import { fetchProgram, Program } from "@/adapter/dynamodb/program";
import { getLoginUser } from "@/auth";
import { fetchUserHistory } from "@/adapter/dynamodb/history";
import { notFound } from "next/navigation";
import { UserDeleteButton } from "./user-delete-button";
import { backupUserId } from "@/lib/consts";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Forbidden } from "@/components/forbidden";

export default async function UserPage(props: {
  params: Promise<Record<string, string>>;
}) {
  const { userId } = await props.params;
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin && loginUser.id !== userId) {
    return <Forbidden />;
  }

  const [user, programPermissions, history] = await Promise.all([
    fetchUser(userId),
    fetchProgramPermissionsByUserId(userId),
    fetchUserHistory(userId),
  ]);

  if (user == null) {
    return notFound();
  }

  // N+1になるけど一旦よしとする
  // 担当番組が多くなったら考える
  const programs_ = await Promise.all(
    programPermissions.map((permission) => fetchProgram(permission.ID)),
  );
  const programs = programs_.filter((p): p is Program => p != null);

  return (
    <div>
      <PageTitle>ユーザー管理</PageTitle>
      <Breadcrumb className="-mt-4 mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/users">ユーザー管理</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{user.CustomClaims?.nhkName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      {/* TODO: historyのページング */}
      <UserInfo user={user} programs={programs} history={history.items} />
      {loginUser.isAdmin &&
        user.ID !== backupUserId &&
        user.ID !== loginUser.id && (
          <div className="flex justify-end">
            <UserDeleteButton user={user} programs={programs} />
          </div>
        )}
    </div>
  );
}
