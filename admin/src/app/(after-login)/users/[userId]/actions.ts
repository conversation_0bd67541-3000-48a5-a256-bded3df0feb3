"use server";

import { backupUserId } from "@/lib/consts";
import {
  deleteUser,
  fetchUser,
  updateUserMemo,
  updateUserType,
} from "@/adapter/dynamodb/user";
import { getLoginUser } from "@/auth";
import { deleteUserHistory } from "@/adapter/dynamodb/history";
import { redirect } from "next/navigation";
import { setFlashMessageCookie } from "@/components/flash-message";
import { fetchProgramPermissionsByUserId } from "@/adapter/dynamodb/program-permission";

export async function updateUserTypeAction(
  userId: string,
  userType: "admin" | "member",
) {
  if (userId === backupUserId) {
    throw new Error("緊急用管理者の権限は変更できません");
  }
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    throw new Error("権限がありません");
  }

  await updateUserType(userId, userType);
  await loginUser.addHistory({
    kind: "admin#switch-user-role",
    userId,
    to: userType,
  });
}

export async function updateUserMemoAction(userId: string, memo: string) {
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    throw new Error("権限がありません");
  }
  await updateUserMemo(userId, memo);
  await loginUser.addHistory({
    kind: "admin#update-user-note",
    userId,
  });
}

export async function deleteUserAction(userId: string) {
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    throw new Error("権限がありません");
  }
  if (userId === backupUserId) {
    throw new Error("緊急用管理者は削除できません");
  }
  const user = await fetchUser(userId);
  if (user == null) {
    throw new Error("ユーザーが見つかりません");
  }
  if (loginUser.id === userId) {
    throw new Error("自分自身を削除することはできません");
  }

  const permissions = await fetchProgramPermissionsByUserId(userId);
  if (permissions.length > 0) {
    throw new Error("担当番組を持っているユーザーは削除できません");
  }

  await deleteUser(userId);
  await deleteUserHistory(userId);
  await loginUser.addHistory({
    kind: "admin#delete-user",
    userId,
  });

  setFlashMessageCookie({
    title: "ユーザーを削除しました",
  });

  redirect("/users/");
}
