"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { User } from "@/adapter/dynamodb/user";
import { useCallback, useState } from "react";
import { updateUserMemoAction, updateUserTypeAction } from "./actions";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { Program } from "@/adapter/dynamodb/program";
import Link from "next/link";
import { LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Textarea } from "@/components/ui/textarea";
import { HistoryItem } from "@/adapter/dynamodb/history";
import { logLabel } from "@/lib/log-label";
dayjs.extend(utc);

export interface UserInfoProps {
  user: User;
  programs: Program[];
  history: HistoryItem[];
}

export function UserInfo(props: UserInfoProps) {
  const { toast } = useToast();
  const [adminDialogOpen, setAdminDialogOpen] = useState(false);
  const handleSwitchAdmin = useCallback(
    (isAdmin: boolean) => {
      updateUserTypeAction(props.user.ID, isAdmin ? "admin" : "member").then(
        () => {
          setAdminDialogOpen(false);
          toast({
            description: "ユーザーの権限を変更しました",
          });
        },
      );
    },
    [props.user.ID, toast],
  );

  const [memo, setMemo] = useState(props.user.Memo);
  const handleSaveMemo = useCallback(() => {
    updateUserMemoAction(props.user.ID, memo ?? "").then(() => {
      toast({
        description: "メモを保存しました",
      });
    });
  }, [props.user.ID, memo, toast]);

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <UserAttributes customClaims={props.user.CustomClaims} />
      </div>

      <div>
        <h2 className="mb-2 text-lg font-semibold">ユーザー権限</h2>
        <div className="flex items-center gap-4">
          <div>
            {props.user.UserType === "admin" ? "管理者" : "一般ユーザー"}
          </div>
          <Button onClick={() => setAdminDialogOpen(true)}>
            {props.user.UserType === "admin"
              ? "一般ユーザーに変更"
              : "管理者に変更"}
          </Button>
        </div>
      </div>

      <div>
        <h2 className="mb-2 text-lg font-semibold">担当番組</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>番組ID</TableHead>
              <TableHead>名前</TableHead>
              <TableHead className="w-6"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {props.programs.map((p) => (
              <TableRow key={p.ID}>
                <TableCell>{p.ID}</TableCell>
                <TableCell>{p.Data.name}</TableCell>
                <TableCell>
                  <Link href={`/programs/${p.ID}`}>
                    <LinkIcon className="h-4 w-4" />
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div>
        <h2 className="mb-2 text-lg font-semibold">メモ</h2>
        <div className="flex flex-col gap-2">
          <Textarea value={memo} onChange={(ev) => setMemo(ev.target.value)} />
          <Button
            className="w-36"
            onClick={handleSaveMemo}
            disabled={memo === props.user.Memo}
          >
            メモを保存する
          </Button>
        </div>
      </div>

      <div>
        <h2 className="mb-2 text-lg font-semibold">操作履歴</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>行動</TableHead>
              <TableHead>時刻</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {props.history
              .filter(
                (h) => h.Data.kind !== "login" && h.Data.kind !== "logout", // Changed '||' to '&&'
              )
              .map((entry) => (
                <TableRow key={entry.ID}>
                  <TableCell>{logLabel(entry.Data.kind)}</TableCell>
                  <TableCell>
                    {dayjs(entry.ID).local().format("YYYY/MM/DD HH:mm:ss")}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>

      <div>
        <h2 className="mb-2 text-lg font-semibold">ログイン履歴</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>行動</TableHead>
              <TableHead>時刻</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {props.history
              .filter(
                (h) => h.Data.kind === "login" || h.Data.kind === "logout",
              )
              .map((entry) => (
                <TableRow key={entry.ID}>
                  <TableCell>{logLabel(entry.Data.kind)}</TableCell>
                  <TableCell>
                    {dayjs(entry.ID).local().format("YYYY/MM/DD HH:mm:ss")}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>
      <AdminSwitchModal
        isOpen={adminDialogOpen}
        setIsOpen={setAdminDialogOpen}
        isAdmin={props.user.UserType === "admin"}
        onConfirm={() => handleSwitchAdmin(props.user.UserType !== "admin")}
      />
    </div>
  );
}

function AdminSwitchModal(props: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  isAdmin: boolean;
  onConfirm: () => void;
}) {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.setIsOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {props.isAdmin ? "一般ユーザーに変更" : "管理者に変更"}
          </DialogTitle>
          <DialogDescription className="pt-4">
            ユーザーの権限を
            {props.isAdmin ? "一般ユーザーに変更" : "管理者に変更"}
            します。よろしいですか？
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4"></div>
        <DialogFooter>
          <Button type="button" variant="destructive" onClick={props.onConfirm}>
            変更する
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function UserAttributes({
  customClaims,
}: {
  customClaims?: Record<string, string>;
}) {
  const isExternalUser = "nhkCompanyName" in (customClaims ?? {});
  const keys = isExternalUser ? externalUserKeys : internalUserKeys;
  return (
    <div
      className="grid gap-4 p-4"
      style={{ gridTemplateColumns: "repeat(2, 1fr)" }}
    >
      {keys.map((key) => (
        <div key={key} className="flex flex-col gap-1">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {keyLabels[key] ?? key}
          </div>
          <div>{customClaims?.[key] ?? "--"}</div>
        </div>
      ))}
    </div>
  );
}

const internalUserKeys = [
  "nhkName",
  "nhkNameKana",
  "email",
  "nhkManNumber",
  "nhkDepartmentCode",
  "nhkDepartmentName",
  "nhkExtensionNumber",
  "nhkDirectNumber",
  "nhkAccountId",
];

const externalUserKeys = [
  "nhkName",
  "nhkNameKana",
  "email",
  "nhkCompanyName",
  "nhkAccountId",
  "nhkDirectNumber",
];

const keyLabels: Record<string, string> = {
  nhkManNumber: "社員番号",
  nhkName: "氏名",
  nhkNameKana: "氏名(カナ)",
  nhkDepartmentCode: "部署コード",
  nhkDepartmentName: "部署名",
  nhkExtensionNumber: "内線番号",
  nhkDirectNumber: "直通番号",
  email: "メールアドレス",
  nhkAccountId: "ID",
  nhkCompanyName: "会社名",
};
