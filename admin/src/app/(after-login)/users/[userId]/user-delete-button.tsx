"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import Form from "next/form";
import { User } from "@/adapter/dynamodb/user";
import { Program } from "@/adapter/dynamodb/program";
import { deleteUserAction } from "./actions";

export function UserDeleteButton(props: { user: User; programs: Program[] }) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async () => {
      await deleteUserAction(props.user.ID);
      setIsOpen(false);
    },
    {},
  );
  const canDeleteUser = props.programs.length === 0;

  return (
    <>
      <Button onClick={() => setIsOpen(true)} variant="destructive">
        ユーザーを削除する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>ユーザーを削除する</DialogTitle>
              {canDeleteUser ? (
                <DialogDescription className="py-4">
                  ユーザーを削除します。 この操作はやり直せません
                </DialogDescription>
              ) : (
                <DialogDescription className="py-4 font-semibold text-red-500 dark:text-red-500">
                  このユーザーは担当番組を持っているため削除できません。担当番組を解除してからユーザーを削除してください。
                </DialogDescription>
              )}
            </DialogHeader>
            <DialogFooter>
              <Button
                type="submit"
                variant="destructive"
                disabled={!canDeleteUser || isPending}
              >
                削除する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
