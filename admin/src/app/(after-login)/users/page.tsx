import { PageTitle } from "@/components/page-title";
import { searchUsersPaging } from "@/adapter/dynamodb/user";
import { UserTableView } from "./user-table-view";
import { backupUserId } from "@/lib/consts";
import { getSearchParams, SearchParams } from "@/utils/search-params";
import { getLoginUser } from "@/auth";
import { Forbidden } from "@/components/forbidden";

export default async function UsersListPage(props: {
  searchParams: SearchParams;
}) {
  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    return <Forbidden />;
  }

  const params = await props.searchParams;
  const search = getSearchParams(params, "search");
  const from = getSearchParams(params, "from");
  const { users, lastKey } = await searchUsersPaging(search, from);

  return (
    <div>
      <PageTitle>ユーザー管理</PageTitle>
      <UserTableView
        data={users.filter((u) => u.ID !== backupUserId)}
        lastKey={lastKey}
      />
    </div>
  );
}
