"use client";

import { User } from "@/adapter/dynamodb/user";
import { Column, createColumnHelper } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button, buttonVariants } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import Link from "next/link";

const columnHelper = createColumnHelper<User>();

export const columns = [
  columnHelper.accessor("CustomClaims.nhkName", {
    id: "name",
    header: ({ column }) => <SortHeader label="名前" column={column} />,
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("CustomClaims.email", {
    id: "email",
    header: ({ column }) => (
      <SortHeader label="メールアドレス" column={column} />
    ),
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor((row) => row.UserType === "admin", {
    id: "userType",
    header: ({ column }) => <SortHeader label="権限" column={column} />,
    cell: (info) => {
      const isAdmin = info.getValue();

      return (
        <div className="font-medium">
          {isAdmin ? (
            <Badge>管理者</Badge>
          ) : (
            <Badge variant="secondary">一般</Badge>
          )}
        </div>
      );
    },
  }),
  columnHelper.accessor(companyNameAccessor, {
    id: "company",
    header: ({ column }) => <SortHeader label="会社名" column={column} />,
    cell: (info) => info.getValue(),
  }),
  columnHelper.display({
    id: "link",
    cell: (info) => {
      const id = info.row.original.ID;
      return (
        <Link
          href={`/users/${id}`}
          className={buttonVariants({ variant: "secondary" })}
        >
          編集
        </Link>
      );
    },
  }),
];

function SortHeader<T>({
  label,
  column,
}: {
  label: string;
  column: Column<User, T>;
}) {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {label}
      <ArrowUpDown className="ml-2 h-4 w-4" />
    </Button>
  );
}

function companyNameAccessor(row: User) {
  if (row.CustomClaims != null && !("nhkDepartmentCode" in row.CustomClaims)) {
    return row.CustomClaims.nhkCompanyName;
  }

  return "";
}
