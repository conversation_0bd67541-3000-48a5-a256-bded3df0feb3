"use server";

import { createProgram } from "@/adapter/dynamodb/program";
import { createProgramPermission } from "@/adapter/dynamodb/program-permission";
import { resetCounterServerSettings } from "@/adapter/lambda/counter-vpc-bridge";
import { getLoginUser } from "@/auth";
import { setFlashMessageCookie } from "@/components/flash-message";
import { redirect } from "next/navigation";
import { z } from "zod";

const ProgramFormSchema = z.object({
  programId: z.string().regex(/^[\w\-]+$/),
  name: z.string(),
  expiresAt: z.string().date(),
});

export async function createProgramAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());

  const parsed = ProgramFormSchema.safeParse(data);
  if (!parsed.success) {
    const isInvalidProgramId = parsed.error.errors.some(
      (e) => e.path[0] === "programId" && e.code === "invalid_string",
    );

    if (isInvalidProgramId) {
      return "invalid_program_id";
    }

    const isInvalidExpiresAt = parsed.error.errors.some(
      (e) => e.path[0] === "expiresAt" && e.code === "invalid_string",
    );

    if (isInvalidExpiresAt) {
      return "invalid_expires_at";
    }

    throw parsed.error;
  }

  const { programId, name, expiresAt } = parsed.data;

  const result = await createProgram(programId, name, expiresAt);
  if (result === "id_already_exists") {
    return "id_already_exists";
  }

  if (!loginUser.isBackupUser()) {
    await createProgramPermission(loginUser.id, programId);
  }

  await loginUser.addHistory({
    kind: "program#create",
    programId,
    name,
  });
  await resetCounterServerSettings();

  await setFlashMessageCookie({
    title: "番組を作成しました",
  });

  redirect("/programs");
}
