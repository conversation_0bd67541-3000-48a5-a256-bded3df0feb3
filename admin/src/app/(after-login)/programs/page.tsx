import { PageTitle } from "@/components/page-title";
import Link from "next/link";
import { getLoginUser } from "@/auth";
import { CreateProgramButton } from "./create-program-button";
import dayjs from "dayjs";
import { cn } from "@/lib/utils";

export default async function ProgramsPage() {
  const loginUser = await getLoginUser();
  const programs = await loginUser.fetchPrograms();

  return (
    <div className="space-y-4">
      <PageTitle>番組管理</PageTitle>
      <ul className="flex flex-col gap-4">
        {programs.map((program) => {
          const expirationWarning =
            program.Data.expiresAt != null &&
            dayjs().add(1, "month").isAfter(dayjs(program.Data.expiresAt));

          return (
            <Link
              href={`/programs/${program.ID}`}
              className="flex gap-6 rounded-md bg-gray-100 p-4 dark:bg-gray-800 md:p-6"
              prefetch={false}
              key={program.ID}
            >
              <div className="flex flex-col gap-1">
                <p className="font-semibold">{program.Data.name}</p>
                <p>ID: {program.ID}</p>
                {program.Data.expiresAt ? (
                  <p
                    className={cn(
                      expirationWarning && "font-semibold text-red-500",
                    )}
                  >
                    期限: {dayjs(program.Data.expiresAt).format("YYYY/MM/DD")}
                  </p>
                ) : null}
              </div>
            </Link>
          );
        })}
      </ul>
      <div className="flex justify-end">
        <CreateProgramButton />
      </div>
    </div>
  );
}
