"use client";

import Form from "next/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { createProgramAction } from "./actions";
import { useActionState, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { DatePicker } from "@/components/date-picker";
import dayjs from "dayjs";

// submitしてエラーの場合値がクリアされてしまうReactのバグがある
// https://github.com/facebook/react/issues/29034
export function CreateProgramButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [date, setDate] = useState<string | undefined>();
  const [error, submitAction, isPending] = useActionState<
    "id_already_exists" | "invalid_program_id" | "invalid_expires_at" | null,
    FormData
  >(async (previousState, formData) => {
    const result = await createProgramAction(formData);
    return result;
  }, null);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>番組を作成する</Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>番組を作成する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  番組名
                </Label>
                <Input id="name" name="name" required className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="programId" className="text-right">
                  番組ID
                </Label>
                <Input
                  id="programId"
                  name="programId"
                  required
                  pattern="^[\w\-]+$"
                  className="col-span-3"
                />
              </div>
              <p
                className={cn(
                  "-mt-2 text-right text-sm opacity-75",
                  error === "invalid_program_id" && "text-red-500",
                )}
              >
                英数字とハイフン、アンダースコアのみ使用できます
              </p>
              {error === "id_already_exists" ? (
                <p className="-mt-2 whitespace-nowrap text-right text-sm font-medium text-red-500">
                  この番組IDは既に使用されています
                </p>
              ) : null}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expiresAt" className="text-right">
                  期限日
                </Label>
                <input type="hidden" name="expiresAt" value={date} />
                <DatePicker
                  date={date ? dayjs(date).toDate() : undefined}
                  onSelect={(date) => setDate(dayjs(date).format("YYYY-MM-DD"))}
                  className="col-span-3 w-full"
                  calendarProps={{
                    toDate: dayjs().add(1, "year").add(6, "month").toDate(),
                    required: true,
                  }}
                />
              </div>
              {error === "invalid_expires_at" ? (
                <p className="-mt-2 whitespace-nowrap text-right text-sm font-medium text-red-500">
                  期限日が無効です
                </p>
              ) : null}
            </div>
            <DialogFooter>
              <Button type="submit" variant="default" disabled={isPending}>
                作成する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
