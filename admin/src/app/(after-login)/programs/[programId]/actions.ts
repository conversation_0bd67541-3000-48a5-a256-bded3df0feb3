"use server";

import { dropMessageTopicTable } from "@/adapter/duckdb";
import {
  createAggAPIAccessToken,
  deleteAggAPIAccessTokenByProgram,
} from "@/adapter/dynamodb/agg-api-access-token";
import {
  createMessageTopic,
  deleteMessageTopic as deleteMessageTopicDynamoDB,
  deleteMessageTopics,
} from "@/adapter/dynamodb/message";
import {
  deleteProgram,
  fetchProgram,
  updateProgram,
} from "@/adapter/dynamodb/program";
import {
  deleteProgramPermission,
  fetchProgramPermissionsByProgramId,
  ProgramPermission,
  updatePermissions,
} from "@/adapter/dynamodb/program-permission";
import { fetchUsers } from "@/adapter/dynamodb/user";
import {
  deleteValidator,
  saveValidator,
  Validator,
} from "@/adapter/dynamodb/validator";
import { deleteVotesByGroup, fetchVoteTopics } from "@/adapter/dynamodb/vote";
import {
  clearMessages,
  resetCounterServerSettings,
} from "@/adapter/lambda/counter-vpc-bridge";
import { deleteMessageTopic } from "@/adapter/s3";
import { getLoginUser, LoginUser } from "@/auth";
import { setFlashMessageCookie } from "@/components/flash-message";
import { redirect } from "next/navigation";
import { z } from "zod";

const CreateMessageTopicSchama = z.object({
  programId: z.string(),
  topicId: z.string().regex(/^[\w\-]+$/),
  topicName: z.string(),
});

export async function createMessageTopicAction(
  formData: FormData,
): Promise<"ok" | "invalid_topic_id" | "id_already_exists"> {
  const data = Object.fromEntries(formData.entries());
  const parsed = CreateMessageTopicSchama.safeParse(data);

  if (!parsed.success) {
    const isInvalidTopicId = parsed.error.errors.some(
      (e) => e.path[0] === "topicId" && e.code === "invalid_string",
    );

    if (isInvalidTopicId) {
      return "invalid_topic_id";
    }

    throw parsed.error;
  }

  const { programId, topicId, topicName } = parsed.data;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const result = await createMessageTopic(programId, topicId, topicName);
  if (result === "id_already_exists") {
    await setFlashMessageCookie({
      title: `ID: ${topicId} は既に存在するトピックIDです`,
      variant: "destructive",
    });
    return "id_already_exists";
  }

  await resetCounterServerSettings();
  await setFlashMessageCookie({
    title: "トピックを作成しました",
  });
  await loginUser.addHistory({
    kind: "program#create-message-topic",
    programId,
    messageTopicId: topicId,
  });

  return "ok";
}

const SaveValidatorSchema = z.object({
  programId: z.string(),
  groupIdGte: z.coerce.number(),
  groupIdLte: z.coerce.number(),
  topicIdGte: z.coerce.number(),
  topicIdLte: z.coerce.number(),
  voteValueGte: z.coerce.number(),
  voteValueLte: z.coerce.number(),
});

export async function saveValidatorAction(formData: FormData) {
  const data = Object.fromEntries(formData.entries());
  const { programId, ...parsed } = SaveValidatorSchema.parse(data);
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const validator: Validator = {
    groupId: {
      gte: parsed.groupIdGte,
      lte: parsed.groupIdLte,
    },
    topicId: {
      gte: parsed.topicIdGte,
      lte: parsed.topicIdLte,
    },
    voteValue: {
      gte: parsed.voteValueGte,
      lte: parsed.voteValueLte,
    },
  };

  let errors = [];
  if (validator.groupId.gte > validator.groupId.lte) {
    await setFlashMessageCookie({
      title: "グループIDの下限値が上限値より大きいです",
      variant: "destructive",
    });
    errors.push({ groupIdGte: "グループIDの下限値が上限値より大きいです" });
  }
  if (validator.topicId.gte > validator.topicId.lte) {
    await setFlashMessageCookie({
      title: "トピックIDの下限値が上限値より大きいです",
      variant: "destructive",
    });
    errors.push({ topicIdGte: "トピックIDの下限値が上限値より大きいです" });
  }
  if (validator.voteValue.gte > validator.voteValue.lte) {
    await setFlashMessageCookie({
      title: "投票値の下限値が上限値より大きいです",
      variant: "destructive",
    });
    errors.push({ voteValueGte: "投票値の下限値が上限値より大きいです" });
  }

  if (errors.length > 0) {
    return { errors };
  }

  await saveValidator(programId, validator);
  await resetCounterServerSettings();

  await loginUser.addHistory({
    kind: "program#update-validation",
    programId,
  });
  await setFlashMessageCookie({
    title: "下限値/上限値を保存しました",
  });

  return "ok";
}

// 権限を持ったユーザーをキーとして値は任意の文字列のオブジェクト
const SaveUserPermissionSchema = z.record(z.string());

export async function saveUserPermissionAction(formData: FormData) {
  const data = Object.fromEntries(formData.entries());
  const { programId, ...users } = SaveUserPermissionSchema.parse(data);

  if (!programId) {
    throw new Error("programId is required");
  }

  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const userIds = Object.keys(users);

  await updatePermissions(programId, userIds);

  await loginUser.addHistory({
    kind: "program#update-permission",
    programId,
  });
  await setFlashMessageCookie({
    title: "番組担当者を保存しました",
  });

  return "ok";
}

const UpdateProgramNameSchema = z.object({
  programId: z.string(),
  programName: z.string(),
});

export async function updateProgramNameAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const { programId, programName } = UpdateProgramNameSchema.parse(data);

  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const original = await fetchProgram(programId);

  await updateProgram(
    programId,
    programName,
    original?.Data.expiresAt,
    original?.Data.scale,
  );

  await loginUser.addHistory({
    kind: "program#update-name",
    programId,
    name: programName,
  });

  await setFlashMessageCookie({
    title: "番組を更新しました",
  });

  return "ok";
}

const UpdateProgramScaleSchema = z.object({
  programId: z.string(),
  programName: z.string(),
  seriesId: z.string().optional(),
  expectedMaxRps: z.coerce.number().min(10000).max(200000).optional(),
  marginHour: z.coerce.number().optional(),
  expiresAt: z.string().date().optional(),
});

export async function updateProgramScaleAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const {
    programId,
    programName,
    seriesId,
    expectedMaxRps,
    marginHour,
    expiresAt,
  } = UpdateProgramScaleSchema.parse(data);

  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  if (
    (seriesId == null && expectedMaxRps != null && marginHour != null) ||
    (seriesId != null && expectedMaxRps == null && marginHour == null)
  ) {
    await setFlashMessageCookie({
      title: "シリーズIDと想定最大RPS、マージン時間を指定してください",
      variant: "destructive",
    });
    return "invalid_input";
  }

  const scale =
    seriesId && expectedMaxRps && marginHour
      ? { seriesId, expectedMaxRps, marginHour }
      : undefined;

  await updateProgram(programId, programName, expiresAt, scale);

  await loginUser.addHistory({
    kind: "program-scale#update",
    programId,
    seriesId,
  });

  await setFlashMessageCookie({
    title: "番組を更新しました",
  });

  return "ok";
}

const DeleteProgramSchema = z.object({
  programId: z.string(),
});

export async function deleteProgramAction(
  formData: FormData,
): Promise<"error" | undefined> {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const { programId } = DeleteProgramSchema.parse(data);
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const permissions = await fetchProgramPermissionsByProgramId(programId);
  const canDelete = canDeleteProgram(loginUser, permissions);
  if (!canDelete) {
    await setFlashMessageCookie({
      title:
        "この番組は担当者がいるため削除できません。担当者を解除してから番組を削除してください。",
      variant: "destructive",
    });
    return "error";
  }

  await loginUser.addHistory({
    kind: "program#delete",
    programId,
  });

  // 番組に紐づく全てのデータを削除する
  const voteTopics = await fetchVoteTopics(programId);
  await Promise.all(
    voteTopics.map((topic) =>
      deleteVotesByGroup(programId, topic.ID.split("#")[1]),
    ),
  );

  await deleteValidator(programId);
  await deleteProgramPermission(programId);
  await deleteMessageTopics(programId);
  await deleteProgram(programId);
  await deleteAggAPIAccessTokenByProgram(programId);
  await resetCounterServerSettings();

  await setFlashMessageCookie({
    title: "番組を削除しました",
  });
  redirect("/programs");
}

export async function searchUserAction(formData: FormData) {
  const data = Object.fromEntries(formData.entries());
  const { programId, query } = z
    .object({
      programId: z.string(),
      query: z.string(),
    })
    .parse(data);

  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  const filteredUsers = (await fetchUsers())
    .filter(
      (user) =>
        user.ID.includes(query) ||
        user.CustomClaims?.nhkName?.includes(query) ||
        user.CustomClaims?.email?.includes(query),
    )
    .map((user) => ({
      ID: user.ID,
      name: user.CustomClaims?.nhkName ?? "",
      email: user.CustomClaims?.email ?? "",
    }));

  const users = filteredUsers.length >= 10 ? [] : filteredUsers;

  return { users, query };
}

const CreateAggAPIAccessTokenFormSchema = z.object({
  programId: z.string(),
  name: z.string(),
});

export async function createAggAPIAccessTokenAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const parsed = CreateAggAPIAccessTokenFormSchema.parse(data);
  if (!loginUser.hasAccessToProgram(parsed.programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${parsed.programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  await createAggAPIAccessToken(parsed.name, parsed.programId);

  await loginUser.addHistory({
    kind: "aggApiToken#create",
    name: parsed.name,
    programId: parsed.programId,
  });

  await setFlashMessageCookie({
    title: "集計APIのアクセス権を作成しました",
  });

  return "ok" as const;
}

const UpdateExpireAtSchema = z.object({
  programId: z.string(),
  expiresAt: z.string().date(),
});

export async function updateExpireAtAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const { programId, expiresAt } = UpdateExpireAtSchema.parse(data);

  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  // 現在のプログラム情報を取得
  const original = await fetchProgram(programId);
  if (original == null) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} が見つかりません`,
      variant: "destructive",
    });
    throw new Error("program_not_found");
  }

  // 期限のみを更新
  await updateProgram(
    programId,
    original.Data.name,
    expiresAt,
    original.Data.scale,
  );

  await loginUser.addHistory({
    kind: "program#update-expires-at",
    programId,
  });

  await setFlashMessageCookie({
    title: "番組の期限日を更新しました",
  });

  return "ok" as const;
}

function canDeleteProgram(
  loginUser: LoginUser,
  permissions: ProgramPermission[],
): boolean {
  if (loginUser.isAdmin) {
    // 管理者ユーザーなら番組を担当しているユーザーが自分だけか、担当しているユーザーがいない場合に削除できる
    return (
      permissions.length === 0 ||
      (permissions.length === 1 && permissions[0].Type.includes(loginUser.id))
    );
  } else {
    // 一般ユーザーなら番組を担当しているユーザーが自分だけかどうかで削除できるか判定
    return (
      permissions.length === 1 && permissions[0].Type.includes(loginUser.id)
    );
  }
}

const DeleteMessageTopicSchema = z.object({
  programId: z.string(),
  messageTopicId: z.string(),
});

export async function deleteMessageTopicAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const { programId, messageTopicId } = DeleteMessageTopicSchema.parse(data);

  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    throw new Error("permission_denied");
  }

  await clearMessages(programId, messageTopicId);
  await deleteMessageTopic(programId, messageTopicId);
  await dropMessageTopicTable(programId, messageTopicId);
  await deleteMessageTopicDynamoDB(programId, messageTopicId);

  await loginUser.addHistory({
    kind: "program#delete-message-topic",
    programId,
    messageTopicId,
  });

  await setFlashMessageCookie({
    title: `メッセージトピック(ID: ${messageTopicId}) を削除しました`,
  });

  return "ok" as const;
}
