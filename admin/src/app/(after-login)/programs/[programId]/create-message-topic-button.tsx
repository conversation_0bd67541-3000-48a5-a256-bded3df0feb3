"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useParams } from "next/navigation";
import { useActionState, useState } from "react";
import Form from "next/form";
import { createMessageTopicAction } from "./actions";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

export function CreateMessageTopicButton() {
  const { programId } = useParams<{ programId: string }>();
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      const result = await createMessageTopicAction(formData);
      if (result === "ok") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        メッセージトピックを作成する
      </Button>
      <Form id="create-message-topic" action={submitAction}>
        <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>メッセージトピックを作成する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <input
                type="hidden"
                id="programId"
                form="create-message-topic"
                name="programId"
                value={programId}
              />
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="topicName" className="text-right">
                  トピック名
                </Label>
                <Input
                  id="topicName"
                  name="topicName"
                  required
                  form="create-message-topic"
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="topicId" className="text-right">
                  トピックID
                </Label>
                <Input
                  id="topicId"
                  name="topicId"
                  required
                  pattern="^[\w\-]+$"
                  form="create-message-topic"
                  className="col-span-3"
                />
              </div>
              <p
                className={cn(
                  "-mt-2 text-right text-sm opacity-75",
                  error === "invalid_topic_id" && "text-red-500",
                )}
              >
                英数字とハイフン、アンダースコアのみ使用できます
              </p>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                form="create-message-topic"
                variant="default"
                disabled={isPending}
              >
                作成する
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Form>
    </>
  );
}
