"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import Form from "next/form";
import { deleteProgramAction } from "./actions";
import { Program } from "@/adapter/dynamodb/program";

export interface DeleteProgramButtonProps {
  program: Program;
  canDeleteProgram: boolean;
}

export function DeleteProgramButton(props: DeleteProgramButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      const result = await deleteProgramAction(formData);
      if (result !== "error") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)} variant="destructive">
        番組を削除する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>番組を削除する</DialogTitle>
              {props.canDeleteProgram ? (
                <DialogDescription className="py-4 text-black">
                  投票データや投票可能な下限値/上限値、担当者設定など番組に関連する全てのデータを削除します。
                  この操作はやり直せません
                </DialogDescription>
              ) : (
                <DialogDescription className="py-4 font-semibold text-red-500 dark:text-red-500">
                  この番組は担当者がいるため削除できません。担当者を解除してから番組を削除してください。
                </DialogDescription>
              )}
            </DialogHeader>
            <input
              type="hidden"
              id="programId"
              name="programId"
              value={props.program.ID}
            />
            <DialogFooter>
              <Button
                type="submit"
                variant="destructive"
                disabled={!props.canDeleteProgram || isPending}
              >
                削除する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
