import { PageTitle } from "@/components/page-title";
import { fetchProgram, Program } from "@/adapter/dynamodb/program";
import { notFound } from "next/navigation";
import {
  fetchProgramPermission,
  fetchProgramPermissionsByProgramId,
  ProgramPermission,
} from "@/adapter/dynamodb/program-permission";
import { fetchVoteTopics } from "@/adapter/dynamodb/vote";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { fetchMessageTopics } from "@/adapter/dynamodb/message";
import { defaultValidator, fetchValidator } from "@/adapter/dynamodb/validator";
import { EditValidatorButton } from "./edit-validator-button";
import { ValidatorView } from "./validator-view";
import { EditUserPermissionButton } from "./edit-user-permission-button";
import { fetchUsers, User } from "@/adapter/dynamodb/user";
import { getLoginUser, LoginUser } from "@/auth";
import { EditProgramNameButton } from "./edit-program-name-button";
import { DeleteProgramButton } from "./delete-program-button";
import { EditSeriesButton } from "./edit-series-button";
import { fetchSeries, fetchComingSeriesBroadcasts } from "@/r7/client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import dayjs from "dayjs";
import { fetchAggAPIAccessTokensByProgram } from "@/adapter/dynamodb/agg-api-access-token";
import { AlertCircle } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { CreateAggAPIAccessTokenButton } from "./create-agg-api-access-token-button";
import {
  UpcomingScales,
  WeekScaleSchedules,
} from "@/components/server-scale/server-components";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { EditExpireAtButton } from "./edit-expire-at-button";
import { TokenDetailButton } from "@/components/agg-api/token-detail-button";
import { MessageTopicListCard, RTCListCard } from "./vote-result-card";

export default async function ProgramPage(props: {
  params: Promise<Record<string, string>>;
}) {
  const { programId } = await props.params;
  const [hasPermission, loginUser] = await checkPermission(programId);
  if (!hasPermission) {
    notFound();
  }

  const [program, permissions, users, voteTopics, messageTopics, validator] =
    await Promise.all([
      fetchProgram(programId),
      fetchProgramPermissionsByProgramId(programId),
      fetchUsers(),
      fetchVoteTopics(programId),
      fetchMessageTopics(programId),
      fetchValidator(programId),
    ]);

  if (!program) {
    notFound();
  }

  const groupTimestampMap = new Map<string, string>();
  voteTopics.forEach((topic) => {
    const groupId = topic.ID.split("#")[1];
    const currentTimestamp = groupTimestampMap.get(groupId);
    if (!currentTimestamp || topic.Timestamp > currentTimestamp) {
      groupTimestampMap.set(groupId, topic.Timestamp);
    }
  });

  const rtcGroupIds = Array.from(groupTimestampMap.entries())
    .sort(([, timestampA], [, timestampB]) =>
      timestampB.localeCompare(timestampA),
    )
    .map(([groupId]) => groupId);

  const messageTopicIds = messageTopics
    .map((topic) => ({
      id: topic.ID.split("#")[1],
      name: topic.Data.name,
      timestamp: topic.Timestamp,
    }))
    .sort((a, b) => b.timestamp.localeCompare(a.timestamp));

  const permittedUsers = users.filter((user) =>
    permissions.some((permission) => permission.Type.includes(user.ID)),
  );

  return (
    <div>
      <PageTitle>
        {program.Data.name} (ID: {programId})
      </PageTitle>
      <Breadcrumb className="-mt-4 mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/programs">番組管理</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{program.Data.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div>
        <div className="flex flex-col gap-6">
          {program.Data.expiresAt &&
            dayjs().add(1, "month").isAfter(dayjs(program.Data.expiresAt)) && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle className="font-semibold">
                  番組の使用
                  {dayjs().isAfter(program.Data.expiresAt)
                    ? "期限が過ぎました"
                    : "期限が近づいています"}
                  {": "}
                  {dayjs(program.Data.expiresAt).format("YYYY/MM/DD")}
                </AlertTitle>
                <AlertDescription>
                  番組の使用
                  {dayjs().isAfter(program.Data.expiresAt)
                    ? "番組の期限が過ぎました。"
                    : "番組の期限が近づいています。"}
                  番組を削除するか、期限を編集して延長してください。
                </AlertDescription>
              </Alert>
            )}
          <RTCListCard programId={programId} rtcGroupIds={rtcGroupIds} />
          <MessageTopicListCard
            programId={programId}
            messageTopicIds={messageTopicIds}
          />
          <Card>
            <CardHeader>
              <CardTitle>担当者リスト</CardTitle>
              <CardDescription>
                担当者に追加するには、NDCIでログインをしてユーザー登録をする必要があります
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="mb-4 h-[240px]">
                <UsersTable users={permittedUsers} />
              </ScrollArea>
              <div className="flex justify-end">
                <EditUserPermissionButton
                  permissions={permissions}
                  permittedUsers={permittedUsers.map((user) => ({
                    ID: user.ID,
                    name: user.CustomClaims?.nhkName || user.ID,
                    email: user.CustomClaims?.email || "",
                  }))}
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>番組表API連携</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <SeriesInfo scale={program.Data.scale} />
              </div>
              <div className="flex justify-end">
                <EditSeriesButton program={program} />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>確定予約一覧</CardTitle>
              <CardDescription>
                スケール変更が開始して完了するまで5~10分程度かかります。
                それを考慮して開始・終了時刻を設定してください。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UpcomingScales programId={programId} />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>繰り返し予約一覧</CardTitle>
              <CardDescription>
                繰り返し予約は毎朝4時に当日分が確定予約になります。当日に作成された予約は、確定予約にはなりません。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WeekScaleSchedules programId={programId} />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>投票可能な下限値/上限値</CardTitle>
              <CardDescription className="pt-1">
                不正防止の観点から、投票可能な下限値と上限値を設定してください
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ValidatorView
                validator={validator?.Data ?? defaultValidator}
                caption={
                  validator
                    ? "番組に設定された下限値/上限値"
                    : "デフォルトの下限値/上限値 (条件未設定のため)"
                }
              />
              <div className="flex justify-end">
                <EditValidatorButton
                  currentValidator={validator?.Data ?? defaultValidator}
                />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>集計APIアクセス権</CardTitle>
            </CardHeader>
            <CardContent>
              <AggAPITokensView program={program} />
              <div className="flex justify-end">
                <CreateAggAPIAccessTokenButton programId={programId} />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>使用期限</CardTitle>
              <CardDescription>番組を使用する期限</CardDescription>
            </CardHeader>
            <CardContent>
              {program.Data.expiresAt && (
                <p>{dayjs(program.Data.expiresAt).format("YYYY/MM/DD")}</p>
              )}
              <div className="flex justify-end">
                <EditExpireAtButton program={program} />
              </div>
            </CardContent>
          </Card>
          <div className="flex justify-end gap-2">
            <DeleteProgramButton
              program={program}
              canDeleteProgram={canDeleteProgram(loginUser, permissions)}
            />
            <EditProgramNameButton program={program} />
          </div>
        </div>
      </div>
    </div>
  );
}

async function checkPermission(
  programId: string,
): Promise<[boolean, LoginUser]> {
  const loginUser = await getLoginUser();
  if (loginUser.isAdmin) {
    return [true, loginUser];
  }

  const permission = await fetchProgramPermission(programId, loginUser.id);
  return [permission != null, loginUser];
}

function canDeleteProgram(
  loginUser: LoginUser,
  permissions: ProgramPermission[],
): boolean {
  if (loginUser.isAdmin) {
    // 管理者ユーザーなら番組を担当しているユーザーが自分だけか、担当しているユーザーがいない場合に削除できる
    return (
      permissions.length === 0 ||
      (permissions.length === 1 && permissions[0].Type.includes(loginUser.id))
    );
  } else {
    // 一般ユーザーなら番組を担当しているユーザーが自分だけかどうかで削除できるか判定
    return (
      permissions.length === 1 && permissions[0].Type.includes(loginUser.id)
    );
  }
}

function UsersTable({ users }: { users: User[] }) {
  return (
    <table className="w-full">
      <thead>
        <tr>
          <th>ユーザー名</th>
          <th>所属</th>
        </tr>
      </thead>
      <tbody>
        {users.map((user) => {
          let belonging = "";
          if (user.CustomClaims != null) {
            if ("nhkDepartmentName" in user.CustomClaims) {
              belonging = user.CustomClaims.nhkDepartmentName;
            } else {
              belonging = user.CustomClaims.nhkCompanyName;
            }
          }

          return (
            <tr key={user.ID}>
              <td>{user.CustomClaims?.nhkName || user.ID}</td>
              <td>{belonging}</td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}

async function SeriesInfo({ scale }: { scale: Program["Data"]["scale"] }) {
  if (scale == null) {
    return <p>番組表APIと連携していません</p>;
  }

  const [series, broadcasts] = await Promise.all([
    fetchSeries(scale.seriesId),
    fetchComingSeriesBroadcasts(scale.seriesId),
  ]);

  if (series == null) {
    return <p>シリーズが見つかりません</p>;
  }

  const interactiveBroadcasts = broadcasts?.result.filter(
    (ep) => ep.misc?.isInteractive,
  );

  return (
    <div>
      <div className="flex justify-between font-semibold *:w-full">
        <div>
          <p className="text-sm">シリーズ名</p>
          <p className="text-xl">{series.name}</p>
        </div>
        <div>
          <p className="text-sm">シリーズID</p>
          <p className="text-xl">{series.id}</p>
        </div>
        <div>
          <p className="mt-2 text-sm">想定最大RPS</p>
          <p className="text-xl">{scale.expectedMaxRps}</p>
        </div>
        <div>
          <p className="mt-2 text-sm">前後マージン</p>
          <p className="text-xl">{scale.marginHour} 時間</p>
        </div>
      </div>
      <Table>
        <TableCaption>
          {interactiveBroadcasts == null || interactiveBroadcasts.length === 0
            ? "サーバースケール予約が作成される予定の放送がありません"
            : "サーバースケール予約が作成される予定の放送"}
        </TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>エピソード名</TableHead>
            <TableHead>放送日時</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {interactiveBroadcasts?.map((be) => (
            <TableRow key={be.id}>
              <TableCell>{be.name}</TableCell>
              <TableCell>
                <span>
                  {dayjs(be.startDate).format("YYYY/MM/DD HH:mm:ss")} ~{" "}
                  {dayjs(be.endDate).format("HH:mm:ss")}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

async function AggAPITokensView({ program }: { program: Program }) {
  const tokens = await fetchAggAPIAccessTokensByProgram(program.ID);

  return (
    <div>
      {tokens.length > 0 ? (
        <Table>
          <TableCaption>番組に紐づけられた集計APIアクセス権</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>名前</TableHead>
              <TableHead className="w-16"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tokens.map((token) => (
              <TableRow key={token.ID}>
                <TableCell>{token.Data.name}</TableCell>
                <TableCell>
                  <TokenDetailButton token={token} program={program} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="space-y-4">
          <p className="text-sm">番組に紐づいた集計APIアクセス権はありません</p>
        </div>
      )}
    </div>
  );
}
