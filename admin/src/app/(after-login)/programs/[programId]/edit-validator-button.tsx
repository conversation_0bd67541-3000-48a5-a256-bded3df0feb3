"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useParams } from "next/navigation";
import { useActionState, useState } from "react";
import Form from "next/form";
import { saveValidatorAction } from "./actions";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Validator } from "@/adapter/dynamodb/validator";

export interface EditValidatorButtonProps {
  currentValidator: Validator;
}

export function EditValidatorButton(props: EditValidatorButtonProps) {
  const { programId } = useParams<{ programId: string }>();
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      const result = await saveValidatorAction(formData);
      if (result === "ok") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        投票可能な下限値/上限値を編集する
      </Button>
      <Form id="edit-validator" action={submitAction}>
        <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>投票可能な下限値/上限値を編集する</DialogTitle>
              <DialogDescription>
                不正防止の観点から投票可能な下限値と上限値を設定してください
              </DialogDescription>
            </DialogHeader>
            <input
              type="hidden"
              id="programId"
              name="programId"
              form="edit-validator"
              value={programId}
            />
            <div className="grid gap-8 py-4">
              <ConditionInput
                label="グループID"
                kind="groupId"
                defaultValue={props.currentValidator}
              />
              <ConditionInput
                label="トピックID"
                kind="topicId"
                defaultValue={props.currentValidator}
              />
              <ConditionInput
                label="投票値"
                kind="voteValue"
                defaultValue={props.currentValidator}
              />
            </div>
            <DialogFooter>
              <Button
                type="submit"
                form="edit-validator"
                variant="default"
                disabled={isPending}
              >
                保存する
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Form>
    </>
  );
}

interface ConditionInputProps {
  label: string;
  kind: keyof Validator;
  defaultValue: Validator;
}

function ConditionInput(props: ConditionInputProps) {
  return (
    <div className="grid gap-2">
      <p className="text-sm font-medium">{props.label}</p>
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label htmlFor={`${props.kind}Gte`}>下限</Label>
          <Input
            id={`${props.kind}Gte`}
            name={`${props.kind}Gte`}
            form="edit-validator"
            defaultValue={props.defaultValue[props.kind]?.gte}
            type="number"
            placeholder="下限"
            className="col-span-1"
          />
        </div>
        <div>
          <Label htmlFor={`${props.kind}Lte`}>上限</Label>
          <Input
            id={`${props.kind}Lte`}
            name={`${props.kind}Lte`}
            form="edit-validator"
            defaultValue={props.defaultValue[props.kind]?.lte}
            type="number"
            placeholder="上限"
            className="col-span-1"
          />
        </div>
      </div>
    </div>
  );
}
