"use client";

import React, { useState, useRef, useActionState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { XIcon } from "lucide-react";
import Form from "next/form";
import { searchUserAction } from "./actions";
import { useParams } from "next/navigation";

// ユーザーの全情報をクライアントに渡さないためのフィルタした型
export interface UserData {
  ID: string;
  name: string;
  email: string;
}

export interface UserSearchInputProps {
  selectedUserIds: string[];
  onSelectUser: (user: UserData) => void;
}

export function UserSearchInput(props: UserSearchInputProps) {
  const params = useParams<{ programId: string }>();
  const [inputValue, setInputValue] = useState("");
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [{ users, query }, submitAction] = useActionState<
    { users: UserData[]; query: string },
    FormData
  >(
    async (_prev, formData) => {
      const { users, query } = await searchUserAction(formData);
      return {
        users: users.filter((user) => !props.selectedUserIds.includes(user.ID)),
        query,
      };
    },
    { users: [], query: "" },
  );

  return (
    <Form className="relative w-full" action={submitAction} ref={formRef}>
      <div className="relative">
        <input type="hidden" name="programId" value={params.programId} />
        <Input
          ref={inputRef}
          name="query"
          type="text"
          value={inputValue}
          onChange={(e) => {
            setInputValue(e.target.value);
            formRef.current?.requestSubmit();
          }}
          placeholder="ユーザーのIDや名前、メールアドレスで検索"
          className="pr-10"
        />
        <Button
          size="icon"
          variant="ghost"
          className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400"
          onClick={() => setInputValue("")}
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>
      {query.length > 0 && inputValue.length > 0 && (
        <ul className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg dark:bg-black">
          {users.map((user) => (
            <li
              key={user.ID}
              className="cursor-pointer px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-800"
              onClick={() => {
                setInputValue("");
                props.onSelectUser(user);
                inputRef.current?.focus();
              }}
            >
              {user.name || user.ID}
            </li>
          ))}
        </ul>
      )}
    </Form>
  );
}
