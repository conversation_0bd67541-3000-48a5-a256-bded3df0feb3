"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import Form from "next/form";
import { updateProgramNameAction } from "./actions";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Program } from "@/adapter/dynamodb/program";

export function EditProgramNameButton(props: { program: Program }) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      const result = await updateProgramNameAction(formData);
      if (result === "ok") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>番組名を編集する</Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>番組名を編集する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <input
                type="hidden"
                id="programId"
                name="programId"
                value={props.program.ID}
              />
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="programName" className="text-right">
                  番組名
                </Label>
                <Input
                  id="programName"
                  name="programName"
                  required
                  defaultValue={props.program.Data.name}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" variant="default" disabled={isPending}>
                保存する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
