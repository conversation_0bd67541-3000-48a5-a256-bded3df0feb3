// https://ui.shadcn.com/charts の Area Chart - Stacked を参考に作成
"use client";

import {
  Area,
  AreaChart,
  CartesianGrid,
  XAxis,
  YAxis,
  TooltipProps,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "@/components/ui/chart";
import { useEffect, useState } from "react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useParams } from "next/navigation";

type VoteResultBySecondItem = {
  timestamp: number;
  choices: { [key: string]: number };
};

interface VoteGraphProps {
  data: VoteResultBySecondItem[];
  options: string[];
}

type SelectedOption = {
  option: string; // topicId.option
  checked: boolean;
  index: number;
}[];

export function VoteGraph(props: VoteGraphProps) {
  const { programId, groupId } = useParams<{
    programId: string;
    groupId: string;
  }>();
  const [data, setData] = useState<VoteResultBySecondItem[]>(props.data);
  const chartConfig = createChartConfig(props.options);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOption>(
    createSelectedOptions(props.options),
  );

  useEffect(() => {
    let isMounted = true;
    let timeoutId: number | null = null;

    async function fetchLoop() {
      try {
        const data = await fetch(
          `/programs/${programId}/rtc/groups/${groupId}/graph/data`,
        ).then((res) => res.json());
        if (isMounted) {
          setData(data.mergedArray);
          timeoutId = window.setTimeout(fetchLoop, 1000);
        }
      } catch (e) {
        // エラー時も1秒後にリトライ
        if (isMounted) {
          timeoutId = window.setTimeout(fetchLoop, 1000);
        }
      }
    }
    fetchLoop();

    return () => {
      isMounted = false;
      if (timeoutId) window.clearTimeout(timeoutId);
    };
  }, [programId, groupId]);

  const flatData: Array<{ [key: string]: number }> = data.map((item) => {
    const obj: { [key: string]: number } = { timestamp: item.timestamp };
    for (const key of props.options) {
      obj[key] = item.choices[key] ?? 0;
    }
    return obj;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle></CardTitle>
        <CardDescription></CardDescription>
      </CardHeader>
      <CardContent>
        <div className="my-4 flex flex-col gap-1">
          {props.options.map((option, idx) => (
            <div key={option} className="flex items-center gap-2">
              <span
                style={{
                  background: getOptionColor(option, props.options),
                }}
                className="mr-1 inline-block h-4 w-4 rounded-full border-gray-500"
              />
              <Label style={{ display: "flex", alignItems: "center", gap: 4 }}>
                <Checkbox
                  id={option}
                  checked={
                    selectedOptions.find((o) => o.option === option)?.checked
                  }
                  onCheckedChange={(v) => {
                    setSelectedOptions((prev) => {
                      return prev.map((o) => {
                        if (o.option === option) {
                          return { ...o, checked: !!v };
                        } else {
                          return o;
                        }
                      });
                    });
                  }}
                />
                {option}
              </Label>
            </div>
          ))}
        </div>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={flatData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="timestamp"
              tickLine={true}
              axisLine={true}
              tickMargin={8}
              tickFormatter={(value) => {
                const date = new Date(value * 1000);
                return date.toLocaleTimeString();
              }}
            />
            <YAxis />
            <ChartTooltip
              cursor={true}
              content={<CustomTooltipContent options={props.options} />}
            />
            {selectedOptions
              .filter((o) => o.checked)
              .map(({ option, index }) => {
                return (
                  <Area
                    key={option}
                    name={option}
                    dataKey={option}
                    type="linear"
                    stackId="1"
                    fill={getOptionColor(option, props.options)}
                    isAnimationActive={false}
                  />
                );
              })}
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}

function createChartConfig(options: string[]): ChartConfig {
  const config: ChartConfig = {};
  let index = 0;
  for (const option of options) {
    config[option] = {
      color: `hsl(var(--chart-${(index % 16) + 1}))`,
    };
    index++;
  }
  return config;
}

function createSelectedOptions(options: string[]): SelectedOption {
  return options.map((option, idx) => ({
    option,
    checked: true,
    index: idx,
  }));
}

function getOptionColor(option: string, options: string[]): string {
  const idx = options.indexOf(option);
  return `hsl(var(--chart-${(idx % 16) + 1}))`;
}

function CustomTooltipContent({
  active,
  payload,
  label,
  options,
}: TooltipProps<any, any> & { options: string[] }) {
  if (active && payload && payload.length) {
    const date = new Date(label * 1000);
    const formattedTime = date.toLocaleTimeString();
    return (
      <div className="rounded-lg border bg-white p-2 shadow-sm dark:bg-black">
        <div className="text-sm font-medium">{formattedTime}</div>
        {payload.map((entry: any, index: number) => (
          <div key={`item-${index}`} className="flex items-center gap-2">
            <div
              className="h-2 w-2 rounded-full"
              style={{ backgroundColor: getOptionColor(entry.name, options) }}
            />
            <span className="text-sm">
              {entry.name}: {entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
}
