import { fetchVoteGroupResult } from "@/adapter/lambda/helper-api";
import { PageTitle } from "@/components/page-title";
import { ResultTable } from "./result-table";
import { ClearButton } from "./clear-button";
import { ReloadButton } from "./reload-button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { fetchProgram } from "@/adapter/dynamodb/program";
import { notFound } from "next/navigation";
import { getLoginUser } from "@/auth";

export default async function RealtimeCounterGroupTablePage(props: {
  params: Promise<Record<string, string>>;
}) {
  const { programId, groupId } = await props.params;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    return notFound();
  }

  const [result, program] = await Promise.all([
    fetchVoteGroupResult(programId, groupId),
    fetchProgram(programId),
  ]);

  if (program == null) {
    return notFound();
  }

  const topicIds = Object.keys(result);
  const options = Array.from(
    new Set(Object.values(result).flatMap((t) => t.options)),
  );
  const hasVote = Object.values(result).some((r) =>
    Object.values(r.choices).some((v) => v > 0),
  );
  const orderedOptions = orderOptions(options);

  return (
    <div>
      <PageTitle>リアルタイムカウンター</PageTitle>
      <Breadcrumb className="-mt-4 mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/programs">番組管理</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/programs/${programId}`}>
              {program.Data.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>リアルタイムカウンター</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>グループID: {groupId}</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>テーブル</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="space-y-4">
        <div className="sticky top-20 z-10 flex justify-end">
          <ReloadButton programId={programId} groupId={groupId} />
        </div>
        <div>
          {!hasVote ? (
            <div>投票がありません</div>
          ) : (
            <ResultTable
              topicIds={topicIds}
              options={orderedOptions}
              result={result}
            />
          )}
        </div>
        <div className="flex justify-end">
          <ClearButton disabled={!hasVote} />
        </div>
      </div>
    </div>
  );
}

function orderOptions(options: string[]): string[] {
  return options.sort((a, b) => {
    // 数値だったら数値として比較し、それ以外は文字列として比較する
    const aNum = parseInt(a, 10);
    const bNum = parseInt(b, 10);
    if (!isNaN(aNum) && !isNaN(bNum)) {
      return aNum - bNum;
    }

    return a.localeCompare(b);
  });
}
