import { VoteGroupResult } from "@/adapter/lambda/helper-api";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ResultTableProps {
  topicIds: string[];
  options: string[];
  result: VoteGroupResult;
}

export function ResultTable(props: ResultTableProps) {
  return (
    <Table className="border">
      <TableHeader>
        <TableRow>
          <TableHead className="w-32">ﾄﾋﾟｯｸID \ ｶﾗﾑ</TableHead>
          {props.options.map((option) => (
            <TableHead key={option} className="border">
              {option}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {props.topicIds.map((topicId) => (
          <TableRow key={topicId}>
            <TableCell>{topicId}</TableCell>
            {props.options.map((option) => (
              <TableCell key={option} className="border">
                {props.result[topicId]?.choices[option] ?? 0}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
