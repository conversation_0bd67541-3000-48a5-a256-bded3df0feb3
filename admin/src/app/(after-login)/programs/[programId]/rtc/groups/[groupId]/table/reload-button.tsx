"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useActionState } from "react";
import { revalidateRtcGroupAction } from "./actions";
import Form from "next/form";

export interface ReloadButtonProps {
  programId: string;
  groupId: string;
}

export function ReloadButton(props: ReloadButtonProps) {
  const [_, submitAction, isPending] = useActionState(async () => {
    await revalidateRtcGroupAction(props.programId, props.groupId);
  }, undefined);

  return (
    <Form action={submitAction}>
      <Button disabled={isPending}>更新</Button>
    </Form>
  );
}
