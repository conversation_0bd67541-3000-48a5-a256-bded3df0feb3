import { getLoginUser } from "@/auth";
import { PageTitle } from "@/components/page-title";
import { notFound } from "next/navigation";
import { VoteGraph } from "./vote-graph";
import { fetchGraphData } from "./get-data";

export default async function RealtimeCounterGroupGraphPage(props: {
  params: Promise<Record<string, string>>;
}) {
  const { programId, groupId } = await props.params;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    return notFound();
  }

  const { mergedArray, options, azs, topicIds } = await fetchGraphData(
    programId,
    groupId,
  );

  return (
    <div>
      <PageTitle>リアルタイムカウンター</PageTitle>
      <div>
        <p>トピック: {topicIds.join(", ")}</p>
        <p>カラム: {options.join(", ")}</p>
        <p>AZ: {azs.join(", ")}</p>
      </div>
      <div>
        <VoteGraph data={mergedArray} options={options} />
      </div>
    </div>
  );
}
