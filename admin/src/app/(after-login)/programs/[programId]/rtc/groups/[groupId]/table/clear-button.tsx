"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useParams } from "next/navigation";
import { useActionState, useState } from "react";
import Form from "next/form";
import { deleteVoteByGroupAction } from "./actions";

export function ClearButton(props: { disabled: boolean }) {
  const { programId, groupId } = useParams<{
    programId: string;
    groupId: string;
  }>();
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState(async () => {
    await deleteVoteByGroupAction(programId, groupId);
    setIsOpen(false);
  }, null);

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        disabled={props.disabled}
        variant="secondary"
      >
        投票データを消去する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>投票データを消去する</DialogTitle>
              <DialogDescription className="py-4">
                投票データを消去します。この操作は取り消せません。よろしいですか？
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="submit" variant="destructive" disabled={isPending}>
                消去する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
