"use server";

import { deleteVotesByGroup } from "@/adapter/dynamodb/vote";
import { resetCounterServerSettings } from "@/adapter/lambda/counter-vpc-bridge";
import { getLoginUser } from "@/auth";
import { setFlashMessageCookie } from "@/components/flash-message";
import { revalidatePath } from "next/cache";

export async function deleteVoteByGroupAction(
  programId: string,
  groupId: string,
): Promise<void> {
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    throw new Error("Access denied");
  }

  await deleteVotesByGroup(programId, groupId);
  await resetCounterServerSettings();
  await setFlashMessageCookie({
    title: "投票データを消去しました",
  });
}

export async function revalidateRtcGroupAction(
  programId: string,
  groupId: string,
) {
  revalidatePath(`/programs/${programId}/rtc/groups/${groupId}`);
}
