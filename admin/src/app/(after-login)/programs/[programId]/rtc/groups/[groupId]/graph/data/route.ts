import "server-only";
import { getLoginUser } from "@/auth";
import { notFound } from "next/navigation";
import { NextResponse } from "next/server";
import { fetchGraphData } from "../get-data";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ programId: string; groupId: string }> },
) {
  const { programId, groupId } = await params;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    return notFound();
  }

  const result = await fetchGraphData(programId, groupId);
  return NextResponse.json(result);
}
