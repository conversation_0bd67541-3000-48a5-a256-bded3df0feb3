import {
  fetchVoteGroupTimeSeriesResult,
  VoteResultBySecondItem,
} from "@/adapter/lambda/helper-api";

export async function fetchGraphData(programId: string, groupId: string) {
  const result = await fetchVoteGroupTimeSeriesResult(programId, groupId);
  const topicIds = Object.keys(result);

  // すべてのtimestampを集める
  const timestampSet = new Set<number>();
  for (const topicId of topicIds) {
    const azs = Object.keys(result[topicId].choices);
    for (const az of azs) {
      for (const item of result[topicId].choices[az]) {
        timestampSet.add(item.timestamp);
      }
    }
  }
  const timestamps = Array.from(timestampSet).sort((a, b) => a - b);

  // optionsリストを topicId.choices.選択肢名 形式で作成
  const options: string[] = [];
  for (const topicId of topicIds) {
    for (const opt of result[topicId].options) {
      options.push(`${topicId}.choices.${opt}`);
    }
  }

  // AZリスト
  const azSet = new Set(
    Object.values(result)
      .flat()
      .flatMap((r) => Object.keys(r.choices)),
  );
  const azs = Array.from(azSet).sort();
  const selectedAz = azs[0];

  const mergedArray: VoteResultBySecondItem[] = timestamps.map((timestamp) => {
    const choices: { [key: string]: number } = {};
    for (const topicId of topicIds) {
      const item = (result[topicId].choices[selectedAz] || []).find(
        (i) => i.timestamp === timestamp,
      );
      for (const opt of result[topicId].options) {
        const key = `${topicId}.choices.${opt}`;
        choices[key] = item ? (item.choices[opt] ?? 0) : 0;
      }
    }
    return { timestamp, choices };
  });

  return { mergedArray, options, azs, topicIds };
}
