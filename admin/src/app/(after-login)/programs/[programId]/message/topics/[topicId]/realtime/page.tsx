import {
  fetchMessages,
  fetchMessagesCount,
} from "@/adapter/lambda/counter-vpc-bridge";
import { PageTitle } from "@/components/page-title";
import { getSearchParams, SearchParams } from "@/utils/search-params";
import { MessageTopicView } from "./message-topic-view";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { fetchProgram } from "@/adapter/dynamodb/program";
import { notFound } from "next/navigation";
import { getLoginUser } from "@/auth";
import { ClearButton } from "./clear-button";

const pageSize = 1000;

export default async function MessageTopicPage(props: {
  params: Promise<Record<string, string>>;
  searchParams: SearchParams;
}) {
  const { programId, topicId } = await props.params;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    return notFound();
  }

  const from = getSearchParams(await props.searchParams, "cursor");
  const [{ messages, cursor }, { count }, program] = await Promise.all([
    fetchMessages(programId, topicId, pageSize, from),
    fetchMessagesCount(programId, topicId),
    fetchProgram(programId),
  ]);

  if (program == null) {
    return notFound();
  }

  const hasNextPage = messages.length === pageSize;
  const keys = Array.from(
    new Set(messages.flatMap((m) => Object.keys(m.value))),
  ).toSorted();

  return (
    <div>
      <PageTitle>メッセージ</PageTitle>
      <Breadcrumb className="-mt-4 mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/programs">番組管理</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/programs/${programId}`}>
              {program.Data.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>メッセージ</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>トピックID: {topicId}</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>リアルタイム</BreadcrumbPage>
          </BreadcrumbItem>
          /
          <BreadcrumbItem>
            <BreadcrumbLink
              href={`/programs/${programId}/message/topics/${topicId}/search`}
              className="underline"
            >
              検索
            </BreadcrumbLink>
            へ
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex justify-between pb-2">
        <p className="py-1 text-sm opacity-80">
          メッセージは1000件単位で表示しています。1000件を超えるメッセージは「次のページ」を押下して取得してください。
          <br />
          前のメッセージを取得する場合はブラウザの戻るボタンをご利用ください。
        </p>
        <h2>メッセージ総数: {count} 件</h2>
      </div>
      <div className="flex h-full flex-col">
        <MessageTopicView
          programId={programId}
          topicId={topicId}
          keys={keys}
          messages={messages}
          nextCursor={hasNextPage ? cursor : undefined}
        />
        <ClearButton
          programId={programId}
          topicId={topicId}
          disabled={messages.length === 0}
          className="mt-4 self-end"
        />
      </div>
    </div>
  );
}
