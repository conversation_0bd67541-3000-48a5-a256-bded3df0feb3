"use client";

import { useActionState, useState } from "react";
import Form from "next/form";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { clearMessagesAction } from "./actions";

export interface ClearButtonProps {
  programId: string;
  topicId: string;
  disabled?: boolean;
  className?: string;
}

export function ClearButton(props: ClearButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [_, submitAction, isPending] = useActionState(async () => {
    await clearMessagesAction(props.programId, props.topicId);
    setIsOpen(false);
  }, undefined);

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        disabled={props.disabled}
        variant="destructive"
        className={props.className}
      >
        メッセージを削除する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>メッセージを削除する</DialogTitle>
              <DialogDescription className="py-4 text-black">
                このトピックのメッセージをすべて削除します。あわせて検索画面のデータも消去されます。
                この操作はやり直せません。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="submit" variant="destructive" disabled={isPending}>
                削除する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
