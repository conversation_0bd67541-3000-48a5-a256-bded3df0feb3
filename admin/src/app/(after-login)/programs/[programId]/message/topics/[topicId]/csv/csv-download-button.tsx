"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";

export function CSVDownloadButton() {
  const [downloading, setDownloading] = useState(false);
  const params = useParams();
  const programId = params.programId as string;
  const topicId = params.topicId as string;

  return (
    <Button
      onClick={async () => {
        setDownloading(true);
        const response = await fetch(
          `/programs/${programId}/message/topics/${topicId}/csv`,
        );
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `message_${programId}_${topicId}.csv`;
        link.click();
        window.URL.revokeObjectURL(url);
        setDownloading(false);
      }}
      disabled={downloading}
    >
      CSVダウンロード
    </Button>
  );
}
