import { fetchMessages } from "@/adapter/duckdb";
import { PageTitle } from "@/components/page-title";
import { SearchParams } from "@/utils/search-params";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { MessagesDumpButton } from "./messages-dump-button";
import { getLoginUser } from "@/auth";
import { notFound } from "next/navigation";
import { fetchProgram } from "@/adapter/dynamodb/program";
import { fetchMessagesCount as fetchRealtimeMessagesCount } from "@/adapter/lambda/counter-vpc-bridge";
import { MessageTopicTable } from "./message-topic-table";
import { CSVDownloadButton } from "../csv/csv-download-button";
import { ECSService, fetchCurrentStatus } from "@/adapter/lambda/helper-api";

export default async function SearchMessageTopicPage(props: {
  params: Promise<Record<string, string>>;
  searchParams: SearchParams;
}) {
  const [{ programId, topicId }, searchParams, loginUser] = await Promise.all([
    props.params,
    props.searchParams,
    getLoginUser(),
  ]);
  if (!loginUser.hasAccessToProgram(programId)) {
    return notFound();
  }

  const [
    program,
    fetchMessagesResult,
    { count: realtimeCount },
    currentServerStatus,
  ] = await Promise.all([
    fetchProgram(programId),
    fetchMessages(
      programId,
      topicId,
      getFilter(searchParams),
      getSort(searchParams),
      getPagination(searchParams),
    ),
    fetchRealtimeMessagesCount(programId, topicId),
    fetchCurrentStatus(),
  ]);
  const isVoting = isVoteOnGoing(currentServerStatus);

  if (program == null) {
    return notFound();
  }

  function PageBreadcrumb() {
    return (
      <Breadcrumb className="-mt-4 mb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/programs">番組管理</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/programs/${programId}`}>
              {program?.Data.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>メッセージ</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>トピックID: {topicId}</BreadcrumbPage>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>検索</BreadcrumbPage>
          </BreadcrumbItem>
          /
          <BreadcrumbItem>
            <BreadcrumbLink
              href={`/programs/${programId}/message/topics/${topicId}/realtime`}
              className="underline"
            >
              リアルタイム
            </BreadcrumbLink>
            へ
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  if (fetchMessagesResult == null) {
    return (
      <div>
        <PageTitle>メッセージ</PageTitle>
        <PageBreadcrumb />
        <div className="flex justify-end">
          <MessagesDumpButton
            programId={programId}
            topicId={topicId}
            isVoting={isVoting}
            count={realtimeCount}
          />
        </div>
        <p>メッセージはありません</p>
      </div>
    );
  }

  const { columnNames, rows } = fetchMessagesResult;
  const totalCount = Number(fetchMessagesResult.totalCount);
  const messageMap = rows.map((row) => {
    const message: Record<string, string | null> = {};
    for (let i = 0; i < columnNames.length; i++) {
      const columnName = columnNames[i];
      const value = row[i];
      if (value != null) {
        message[columnName] = value.toString();
      } else {
        message[columnName] = value;
      }
    }
    return message;
  });

  // sv_timestampは最後の列にする
  const sortedColumnNames = [
    ...columnNames.filter((name) => name !== "sv_timestamp"),
    "sv_timestamp",
  ];

  return (
    <div>
      <PageTitle>メッセージ</PageTitle>
      <PageBreadcrumb />
      <div className="mb-2 flex items-center justify-between gap-2">
        <CSVDownloadButton />
        <div className="flex gap-2">
          <div className="flex flex-col justify-center text-sm">
            <p>
              メッセージ件数: {realtimeCount} / 検索用DBに登録された件数:{" "}
              {totalCount}
            </p>
            {realtimeCount > totalCount && (
              <p>
                検索DBを更新して{realtimeCount - totalCount}
                件のメッセージを取得してください
              </p>
            )}
          </div>
          <MessagesDumpButton
            programId={programId}
            topicId={topicId}
            isVoting={isVoting}
            count={realtimeCount}
          />
        </div>
      </div>
      <MessageTopicTable
        columnNames={sortedColumnNames}
        messages={messageMap}
        totalCount={Number(totalCount)}
      />
    </div>
  );
}

function getFilter(searchParams: {
  [key: string]: string | string[] | undefined;
}): { [key: string]: string } {
  const filteredParams: { [key: string]: string } = {};
  for (const key in searchParams) {
    if (key === "sort" || key === "page") {
      continue;
    }

    const value = Array.isArray(searchParams[key])
      ? searchParams[key][0]
      : searchParams[key];

    if (value) {
      if (key === "sv_timestamp") {
        filteredParams[key] = value.replaceAll("/", "-");
      } else {
        filteredParams[key] = value;
      }
    }
  }

  return filteredParams;
}

function getSort(searchParams: {
  [key: string]: string | string[] | undefined;
}): { key: string; desc: boolean } | null {
  const sort = searchParams["sort"];
  if (typeof sort !== "string" || sort === "") {
    return null;
  }

  const [id, direction] = sort.split(",");
  return {
    key: id,
    desc: direction === "desc",
  };
}

function getPagination(searchParams: {
  [key: string]: string | string[] | undefined;
}): { pageIndex: number; pageSize: number } {
  const page = searchParams["page"] ?? "1";
  const pageSize = searchParams["pageSize"] ?? "100";
  return { pageIndex: Number(page) - 1, pageSize: Number(pageSize) };
}

function isVoteOnGoing(currentServerStatus: ECSService[]): boolean {
  return currentServerStatus.some((service) =>
    service.taskDefinition.includes("Large"),
  );
}
