"use client";

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MessageItem } from "@/adapter/lambda/counter-vpc-bridge";
import { columns } from "@/app/(after-login)/logs/columns";
import { createColumns } from "./columns";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { Button, buttonVariants } from "@/components/ui/button";
import { ReloadButton } from "./reload-button";

interface DataTableProps {
  programId: string;
  topicId: string;
  nextURL?: string;
  keys: string[];
  data: MessageItem[];
}

export function MessageTopicTable({
  programId,
  topicId,
  nextURL,
  keys,
  data,
}: DataTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const table = useReactTable({
    data,
    columns: createColumns(keys),
    state: {
      columnFilters,
      sorting,
    },
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnFiltersChange: setColumnFilters,
  });

  const nextPageButton = nextURL ? (
    <Link href={nextURL} className={buttonVariants()}>
      次のページ
    </Link>
  ) : (
    <Button disabled>次のページ</Button>
  );

  return (
    <div>
      <div className="flex justify-between pb-4">
        <ReloadButton programId={programId} topicId={topicId} />
        <div className="flex gap-2">{nextPageButton}</div>
      </div>
      <div className="h-[calc(100vh-24rem)] overflow-y-auto rounded-md border">
        <Table>
          <TableHeader className="sticky top-0 z-10 bg-white dark:bg-black">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      <div>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                        <Input
                          className="my-2"
                          onChange={(event) =>
                            header.column.setFilterValue(event.target.value)
                          }
                          placeholder="フィルタ"
                          type="text"
                          value={
                            (header.column.getFilterValue() as string) ?? ""
                          }
                        />
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
