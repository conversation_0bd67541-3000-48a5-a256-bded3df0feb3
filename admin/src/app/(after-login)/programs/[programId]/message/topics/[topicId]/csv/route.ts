import { fetchMessageTopicCSV } from "@/adapter/duckdb";
import { getLoginUser } from "@/auth";

export async function GET(
  _request: Request,
  { params }: { params: Promise<Record<string, string>> },
) {
  const { programId, topicId } = await params;
  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    return new Response("Unauthorized", { status: 401 });
  }

  let buffer = await fetchMessageTopicCSV(programId, topicId);
  if (buffer == null) {
    return new Response("Not Found", { status: 404 });
  }

  // BOMを付与する
  if (
    buffer.length < 3 ||
    buffer[0] !== 0xef ||
    buffer[1] !== 0xbb ||
    buffer[2] !== 0xbf
  ) {
    const BOM = Buffer.from([0xef, 0xbb, 0xbf]);
    buffer = Buffer.concat([BOM, buffer]);
  }

  const headers = new Headers();
  headers.append(
    "Content-Disposition",
    'attachment; filename="attachment.csv"',
  );
  headers.append("Content-Type", "application/csv");

  return new Response(buffer, {
    headers,
  });
}
