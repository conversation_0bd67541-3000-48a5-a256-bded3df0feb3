"use client";

import { MessageItem } from "@/adapter/lambda/counter-vpc-bridge";
import { MessageTopicTable } from "./message-topic-table";
import { useSearchParams } from "next/navigation";

interface MessageTopicViewProps {
  programId: string;
  topicId: string;
  keys: string[];
  messages: MessageItem[];
  nextCursor?: string;
}

export function MessageTopicView(props: MessageTopicViewProps) {
  const searchParams = useSearchParams();

  function getNextURL(nextCursor?: string): string | undefined {
    if (!nextCursor) {
      return undefined;
    }
    const params = new URLSearchParams(searchParams.toString());
    params.set("cursor", nextCursor);

    return `/programs/${props.programId}/message/topics/${props.topicId}/realtime?${params.toString()}`;
  }
  const nextURL = getNextURL(props.nextCursor);

  return (
    <MessageTopicTable
      programId={props.programId}
      topicId={props.topicId}
      nextURL={nextURL}
      keys={props.keys}
      data={props.messages}
    />
  );
}
