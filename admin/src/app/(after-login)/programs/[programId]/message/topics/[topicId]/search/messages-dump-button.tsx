"use client";

import { But<PERSON> } from "@/components/ui/button";
import Form from "next/form";
import { dumpMessagesAction } from "./actions";
import { useActionState } from "react";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

interface MessagesDumpButtonProps {
  programId: string;
  topicId: string;
  isVoting: boolean;
  count: number;
}

export function MessagesDumpButton(props: MessagesDumpButtonProps) {
  const { programId, topicId } = props;
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_, formData) => {
      await dumpMessagesAction(formData);
    },
    {},
  );
  const disabledMessage = props.isVoting
    ? "スケール中のため検索DBを更新できません"
    : props.count === 0
      ? "メッセージ件数が0件のため検索DBを更新できません"
      : undefined;

  return (
    <Form action={submitAction}>
      <input type="hidden" name="programId" value={programId ?? ""} />
      <input type="hidden" name="topicId" value={topicId ?? ""} />
      {!disabledMessage ? (
        <Button type="submit" disabled={isPending}>
          検索DBを更新する
        </Button>
      ) : (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button className="opacity-50" type="button">
                検索DBを更新する
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{disabledMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </Form>
  );
}
