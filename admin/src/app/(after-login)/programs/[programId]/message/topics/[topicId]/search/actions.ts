"use server";

import { dumpMessages } from "@/adapter/lambda/counter-vpc-bridge";
import { getLoginUser } from "@/auth";
import { setFlashMessageCookie } from "@/components/flash-message";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const DumpMessagesSchema = z.object({
  programId: z.string(),
  topicId: z.string(),
});

export async function dumpMessagesAction(formData: FormData) {
  const data = DumpMessagesSchema.parse(Object.fromEntries(formData));
  const user = await getLoginUser();
  if (!user.hasAccessToProgram(data.programId)) {
    throw new Error("Unauthorized");
  }

  const result = await dumpMessages(data.programId, data.topicId);
  revalidatePath(
    `/programs/${data.programId}/message/topics/${data.topicId}/search`,
  );

  await setFlashMessageCookie({
    title: "検索DBを更新しました",
  });

  return result;
}
