"use client";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSearchParams } from "next/navigation";

interface PaginationComponentProps {
  totalCount: number;
  onPageChange: (page: number) => void;
}

export function MessagePagination({
  totalCount,
  onPageChange,
}: PaginationComponentProps) {
  const searchParams = useSearchParams();
  const pageIndex = searchParams.get("page")
    ? Number(searchParams.get("page")) - 1
    : 0;
  const pageSize = Number(searchParams.get("pageSize")) || 100;

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  // Calculate which page numbers to show
  const getPageNumbers = () => {
    const pages = [];

    // Always show first page
    pages.push(0);

    // Show ellipsis after first page if current page is far from start
    if (pageIndex > 2) {
      pages.push(-1); // -1 represents ellipsis
    }

    // Show 1 page before current page
    if (pageIndex > 1) {
      pages.push(pageIndex - 1);
    }

    // Show current page if not first or last
    if (pageIndex > 0 && pageIndex < totalPages - 1) {
      pages.push(pageIndex);
    }

    // Show 1 page after current page
    if (pageIndex < totalPages - 2) {
      pages.push(pageIndex + 1);
    }

    // Show ellipsis before last page if current page is far from end
    if (pageIndex < totalPages - 3) {
      pages.push(-2); // -2 represents ellipsis (using different value to avoid React key conflicts)
    }

    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pages.push(totalPages - 1);
    }

    // Remove duplicates
    return [...new Set(pages)];
  };

  const pageNumbers = getPageNumbers();

  // Check if we're on first or last page for disabling buttons
  const isFirstPage = pageIndex === 0;
  const isLastPage = pageIndex === totalPages - 1;

  return (
    <Pagination>
      <PaginationContent>
        {/* Previous page button */}
        <PaginationItem>
          <button
            className={`flex h-9 w-9 items-center justify-center rounded-md border ${
              isFirstPage
                ? "pointer-events-none opacity-50"
                : "hover:bg-accent hover:text-accent-foreground"
            }`}
            onClick={() => !isFirstPage && onPageChange(pageIndex - 1)}
            disabled={isFirstPage}
            aria-label="前のページへ"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        </PaginationItem>

        {/* Page numbers */}
        {pageNumbers.map((page, index) => {
          // Render ellipsis
          if (page < 0) {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }

          // Render page number
          return (
            <PaginationItem key={page}>
              <PaginationLink
                className="cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(page);
                }}
                isActive={page === pageIndex}
              >
                {page + 1}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        {/* Next page button */}
        <PaginationItem>
          <button
            className={`flex h-9 w-9 items-center justify-center rounded-md border ${
              isLastPage
                ? "pointer-events-none opacity-50"
                : "hover:bg-accent hover:text-accent-foreground"
            }`}
            onClick={() => !isLastPage && onPageChange(pageIndex + 1)}
            disabled={isLastPage}
            aria-label="次のページへ"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
