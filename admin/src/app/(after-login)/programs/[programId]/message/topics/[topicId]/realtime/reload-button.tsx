"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useActionState } from "react";
import { revalidateMessageTopicAction } from "./actions";
import Form from "next/form";

export interface ReloadButtonProps {
  programId: string;
  topicId: string;
}

export function ReloadButton(props: ReloadButtonProps) {
  const [_, submitAction, isPending] = useActionState(async () => {
    await revalidateMessageTopicAction(props.programId, props.topicId);
  }, undefined);

  return (
    <Form action={submitAction}>
      <Button disabled={isPending}>更新</Button>
    </Form>
  );
}
