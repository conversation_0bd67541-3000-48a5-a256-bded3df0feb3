"use client";

import { MessageItem } from "@/adapter/lambda/counter-vpc-bridge";
import { Button } from "@/components/ui/button";
import { Column, ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { ArrowUpDown } from "lucide-react";

export function createColumns(keys: string[]): ColumnDef<MessageItem>[] {
  const dynamicColumns: ColumnDef<MessageItem>[] = keys
    .filter((key) => key !== "sv_timestamp")
    .map((key) => ({
      accessorKey: `value.${key}`,
      header: ({ column }) => <SortHeader label={key} column={column} />,
      filterFn: "includesString",
    }));

  return [
    {
      accessorKey: "value.sv_timestamp",
      header: ({ column }) => <SortHeader label="日時" column={column} />,
      cell: (props) => {
        const dateStr = props.getValue() as string;
        return dayjs(dateStr).format("YYYY/MM/DD HH:mm:ss");
      },
      filterFn: (row, columnId, filterValue) => {
        const dateStr = row.getValue(columnId) as string;
        const date = dayjs(dateStr).format("YYYY/MM/DD HH:mm:ss");
        return date.includes(filterValue);
      },
    },
    ...dynamicColumns,
  ];
}

function SortHeader<T>({
  label,
  column,
}: {
  label: string;
  column: Column<MessageItem, T>;
}) {
  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {label}
      <ArrowUpDown className="ml-2 h-4 w-4" />
    </Button>
  );
}
