"use server";

import { dropMessageTopicTable } from "@/adapter/duckdb";
import { clearMessages } from "@/adapter/lambda/counter-vpc-bridge";
import { deleteMessageTopic } from "@/adapter/s3";
import { setFlashMessageCookie } from "@/components/flash-message";
import { logger } from "@/logger";
import { revalidatePath } from "next/cache";

export async function revalidateMessageTopicAction(
  programId: string,
  topicId: string,
) {
  revalidatePath(`/programs/${programId}/message/topics/${topicId}`);
}

export async function clearMessagesAction(programId: string, topicId: string) {
  const result = await clearMessages(programId, topicId);
  await deleteMessageTopic(programId, topicId);
  await dropMessageTopicTable(programId, topicId);

  logger.info(
    {
      programId,
      topicId,
      result,
    },
    "clearMessagesAction",
  );
  revalidatePath(`/programs/${programId}/message/topics/${topicId}`);

  await setFlashMessageCookie({
    title: "メッセージを削除しました",
  });

  return "ok";
}
