"use client";

import { use<PERSON><PERSON>back, useOptimistic, useState } from "react";
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import {
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Table,
} from "@/components/ui/table";
import {
  Column,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";
import { MessagePagination } from "./message-pagination";

interface Message {
  [key: string]: string | null;
}

const columnHelper = createColumnHelper<Message>();

function createColumn(columnNames: string[]) {
  return columnNames
    .filter((name) => name !== "sv_id")
    .map((name) =>
      columnHelper.accessor(name, {
        header: ({ column }) => {
          let label = name;
          switch (name) {
            case "id":
              label = "ID";
              break;
            case "sv_timestamp":
              label = "受信日時";
              break;
            default:
          }
          return <SortHeader label={label} column={column} />;
        },
        cell: (info) => {
          if (info.column.id === "sv_timestamp") {
            const date = new Date(info.getValue() as string);
            return date.toLocaleDateString("ja-JP", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            });
          }
          return info.getValue();
        },
      }),
    );
}

interface MessageTopicTableProps {
  columnNames: string[];
  messages: Message[];
  totalCount: number;
}

export function MessageTopicTable(props: MessageTopicTableProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [sorting, setSorting] = useState<SortingState>(
    getSortingState(searchParams),
  );
  const [pagination, setPagination] = useState<{
    pageIndex: number;
    pageSize: number;
  }>(getPagination(searchParams));
  const table = useReactTable({
    data: props.messages,
    columns: createColumn(props.columnNames),
    getCoreRowModel: getCoreRowModel(),
    manualFiltering: true,
    manualSorting: true,
    manualPagination: true,
    rowCount: props.totalCount,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: (state) => {
      if (typeof state === "function") {
        setSorting((prev) => {
          const newState = state(prev);
          updateSort(newState);
          return newState;
        });
      } else {
        updateSort(state);
        setSorting(state);
      }
    },
    onPaginationChange: (pagination) => {
      if (typeof pagination === "function") {
        setPagination((prev) => {
          const newState = pagination(prev);
          updatePagination(newState);
          return newState;
        });
      } else {
        updatePagination(pagination);
        setPagination(pagination);
      }
    },
  });

  function updateSort(state: SortingState) {
    if (state.length > 0) {
      const params = new URLSearchParams(searchParams.toString());
      const sort = state[0];
      const sortDirection = sort.desc ? "desc" : "asc";
      params.set("sort", `${sort.id},${sortDirection}`);
      router.push(`?${params.toString()}`);
    }
  }

  function updatePagination(pagination: {
    pageIndex: number;
    pageSize: number;
  }) {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", `${pagination.pageIndex + 1}`);
    router.push(`?${params.toString()}`);
  }

  const handleFilterChange = useCallback(
    (columnId: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(columnId, value);
      router.push(`?${params.toString()}`);
    },
    [router, searchParams],
  );

  return (
    <div>
      <div className="h-[calc(100vh-20rem)] overflow-y-auto rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      <FilterInput
                        id={header.id}
                        onChange={(value) =>
                          handleFilterChange(header.id, value)
                        }
                      />
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns().length}
                  className="h-24 text-center"
                >
                  メッセージはありません
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="p-2">
        <MessagePagination
          totalCount={props.totalCount}
          onPageChange={(pageIndex) => {
            let newState: any;
            setPagination((prev) => {
              newState = { ...prev, pageIndex };
              return newState;
            });
            updatePagination(newState);
          }}
        />
      </div>
    </div>
  );
}

function getSortingState(searchParams: URLSearchParams): SortingState {
  const sort = searchParams.get("sort");
  if (sort) {
    const [columnId, direction] = sort.split(",");
    return [
      {
        id: columnId,
        desc: direction === "desc",
      },
    ];
  }
  return [];
}

function getPagination(searchParams: URLSearchParams) {
  const page = searchParams.get("page") ?? "1";
  const pageSize = searchParams.get("pageSize") ?? "100";
  return { pageIndex: Number(page) - 1, pageSize: Number(pageSize) };
}

function FilterInput(props: { id: string; onChange: (value: string) => void }) {
  const searchParams = useSearchParams();
  const [optimisticValue, updateValue] = useOptimistic(
    searchParams.get(props.id) ?? "",
    (prev: string, newMessage: string) => newMessage,
  );

  return (
    <Input
      className="my-2"
      onChange={(ev) => {
        updateValue(ev.target.value);
        props.onChange(ev.target.value);
      }}
      value={optimisticValue}
      placeholder="フィルタ"
      type="text"
    />
  );
}

function SortHeader<T>({
  label,
  column,
}: {
  label: string;
  column: Column<Message, T>;
}) {
  const sorted = column.getIsSorted();
  let Icon = ArrowUpDown;
  switch (sorted) {
    case "asc":
      Icon = ArrowDown;
      break;
    case "desc":
      Icon = ArrowUp;
      break;
    default:
  }

  return (
    <Button
      variant="ghost"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {label}

      <Icon className="ml-2 h-4 w-4" />
    </Button>
  );
}
