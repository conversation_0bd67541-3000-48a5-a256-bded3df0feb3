"use client";

import React, { useState, useRef, useEffect, useDeferredValue } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { XIcon } from "lucide-react";
import type { components } from "@/r7/schema";

interface SeriesSearchInputProps {
  onSelectSeries: (
    series: components["schemas"]["TVSeries"] | undefined,
  ) => void;
}

export function SeriesSearchInput(props: SeriesSearchInputProps) {
  const [inputValue, setInputValue] = useState("");
  const deferredInput = useDeferredValue(inputValue);
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="relative mb-4 w-full">
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="シリーズの名前で検索"
          className="pr-10"
        />
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400"
          onClick={() => setInputValue("")}
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>
      <SeriesSuggestions
        input={deferredInput}
        onSelect={(series) => {
          setInputValue("");
          props.onSelectSeries(series);
          // inputRef.current?.focus();
        }}
      />
    </div>
  );
}

function SeriesSuggestions(props: {
  input: string;
  onSelect: (series: components["schemas"]["TVSeries"]) => void;
}) {
  const [suggestions, setSuggestions] = useState<
    components["schemas"]["TVSeries"][]
  >([]);

  useEffect(() => {
    if (props.input === "") {
      setSuggestions([]);
      return;
    }

    searchSeries(props.input).then((data) => {
      setSuggestions(data?.result ?? []);
    });
  }, [props.input]);

  if (props.input === "" || suggestions.length === 0) {
    return null;
  }

  return (
    <ul className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg dark:bg-black">
      {suggestions.map((series) => (
        <li
          key={series.id}
          className="cursor-pointer px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={() => {
            props.onSelect(series);
          }}
        >
          {series.name}
        </li>
      ))}
    </ul>
  );
}

async function searchSeries(
  word: string,
): Promise<components["schemas"]["TVSeriesList"] | undefined> {
  const url = new URL("https://api.nhk.jp/r7/s/extended2.json");
  url.searchParams.append("modeOfItem", "tv");
  url.searchParams.append("type", "series");
  url.searchParams.append("word", `"${word}"`);

  const res = await fetch(url.toString());
  if (!res.ok) {
    return undefined;
  }

  const data = (await res.json()) as components["schemas"]["Search2Response"];
  return data.result?.tvseries;
}
