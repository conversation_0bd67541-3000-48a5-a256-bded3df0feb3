"use client";

import { useActionState, useState } from "react";
import Form from "next/form";
import dayjs from "dayjs";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Program } from "@/adapter/dynamodb/program";
import { DatePicker } from "@/components/date-picker";
import { updateExpireAtAction } from "./actions";

export function EditExpireAtButton(props: { program: Program }) {
  const [isOpen, setIsOpen] = useState(false);
  const [date, setDate] = useState<string | undefined>(
    props.program.Data.expiresAt,
  );
  const [error, submitAction, isPending] = useActionState<
    "invalid_expires_at" | null,
    FormData
  >(async (previousState, formData) => {
    const result = await updateExpireAtAction(formData);
    if (result === "ok") {
      setIsOpen(false);
    }
    return null;
  }, null);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>使用期限を編集する</Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>使用期限を編集する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <input
                type="hidden"
                id="programId"
                name="programId"
                value={props.program.ID}
              />
              <input type="hidden" name="expiresAt" value={date} />
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expiresAt" className="text-right">
                  期限日
                </Label>
                <DatePicker
                  date={date ? dayjs(date).toDate() : undefined}
                  onSelect={(date) => setDate(dayjs(date).format("YYYY-MM-DD"))}
                  className="col-span-3 w-full"
                  calendarProps={{
                    toDate: dayjs().add(1, "year").add(6, "month").toDate(),
                    required: true,
                  }}
                />
              </div>
              {error === "invalid_expires_at" ? (
                <p className="-mt-2 whitespace-nowrap text-right text-sm font-medium text-red-500">
                  期限日が無効です
                </p>
              ) : null}
            </div>
            <DialogFooter>
              <Button type="submit" variant="default" disabled={isPending}>
                保存する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
