import { Validator } from "@/adapter/dynamodb/validator";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function ValidatorView({
  validator,
  caption,
}: {
  validator: Validator;
  caption: string;
}) {
  const conditions = [
    ["グループID", validator.groupId],
    ["トピックID", validator.topicId],
    ["投票値", validator.voteValue],
  ] as const;

  return (
    <Table>
      <TableCaption>{caption}</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-32">項目</TableHead>
          <TableHead className="w-32">下限</TableHead>
          <TableHead className="w-32">上限</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {conditions.map(([label, condition]) => (
          <TableRow key={label}>
            <TableCell className="font-medium">{label}</TableCell>
            <TableCell>{condition.gte}</TableCell>
            <TableCell>{condition.lte}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
