"use client";

import Link from "next/link";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { useActionState, useState } from "react";
import { XIcon } from "lucide-react";
import { CreateMessageTopicButton } from "./create-message-topic-button";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteMessageTopicAction } from "./actions";
import Form from "next/form";
import { Checkbox } from "@/components/ui/checkbox";

interface RTCListCardProps {
  programId: string;
  rtcGroupIds: string[];
}

export function RTCListCard({ programId, rtcGroupIds }: RTCListCardProps) {
  const [filter, setFilter] = useState("");

  return (
    <Card>
      <CardHeader>
        <CardTitle>リアルタイムカウンター</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-1">
          <Input
            placeholder="グループIDでフィルタする"
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          />
          <Button variant="outline" size="icon" onClick={() => setFilter("")}>
            <XIcon />
          </Button>
        </div>
        <ScrollArea className="h-[360px]">
          {rtcGroupIds.length === 0 ? (
            <div>リアルタイムカウンター投票を受付したら表示されます</div>
          ) : (
            <ul className="flex flex-col gap-2">
              {rtcGroupIds
                .filter((id) => id.toLowerCase().includes(filter.toLowerCase()))
                .map((groupId) => (
                  // 紅白向けのグラフ画面へのリンクは隠しておく
                  // <li key={groupId} className="grid grid-cols-3 gap-2">
                  //   <div className="flex items-center">{groupId}</div>
                  //   <Link
                  //     href={`/programs/${programId}/rtc/groups/${groupId}/graph`}
                  //     className={cn(
                  //       buttonVariants({ variant: "outline" }),
                  //       "w-full justify-center",
                  //     )}
                  //   >
                  //     グラフ
                  //   </Link>
                  //   <Link
                  //     href={`/programs/${programId}/rtc/groups/${groupId}/table`}
                  //     className={cn(
                  //       buttonVariants({ variant: "outline" }),
                  //       "w-full justify-center",
                  //     )}
                  //   >
                  //     テーブル
                  //   </Link>
                  // </li>
                  <li key={groupId}>
                    <Link
                      href={`/programs/${programId}/rtc/groups/${groupId}/table`}
                      className={cn(
                        buttonVariants({ variant: "outline" }),
                        "w-full justify-start",
                      )}
                    >
                      {groupId}
                    </Link>
                  </li>
                ))}
            </ul>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

interface MessageTopicListCardProps {
  programId: string;
  messageTopicIds: {
    id: string;
    name: string;
  }[];
}

export function MessageTopicListCard({
  programId,
  messageTopicIds,
}: MessageTopicListCardProps) {
  const [filter, setFilter] = useState("");
  const [deleteTopicId, setDeleteTopicId] = useState<string | null>(null);
  const [deleteConfirmed, setDeleteConfirmed] = useState(false);

  const [_, submitAction, isPending] = useActionState<{}, FormData>(
    async (_, formData) => {
      const result = await deleteMessageTopicAction(formData);
      if (result === "ok") {
        setDeleteTopicId(null);
      }
    },
    {},
  );

  const currentTopicName =
    deleteTopicId &&
    (messageTopicIds.find((t) => t.id === deleteTopicId)?.name ||
      deleteTopicId);

  return (
    <Card>
      <CardHeader>
        <CardTitle>メッセージ</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-1">
          <Input
            placeholder="トピックIDもしくは名前でフィルタする"
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          />
          <Button variant="outline" size="icon" onClick={() => setFilter("")}>
            <XIcon />
          </Button>
        </div>
        <ScrollArea className="h-[360px]">
          {messageTopicIds.length === 0 ? (
            <div>
              メッセージトピックを作成することでメッセージ投票を受付できます
            </div>
          ) : (
            <ul className="flex flex-col gap-2">
              {messageTopicIds
                .filter(
                  (m) =>
                    m.id.toLowerCase().includes(filter.toLowerCase()) ||
                    m.name.toLowerCase().includes(filter.toLowerCase()),
                )
                .map((topic) => (
                  <li key={topic.id} className="flex justify-between gap-2">
                    <div className="flex w-48 items-center">{topic.name}</div>
                    <div className="flex gap-2">
                      <Link
                        href={`/programs/${programId}/message/topics/${topic.id}/realtime`}
                        className={cn(
                          buttonVariants({ variant: "outline" }),
                          "w-80 justify-center",
                        )}
                      >
                        リアルタイム
                      </Link>
                      <Link
                        href={`/programs/${programId}/message/topics/${topic.id}/search`}
                        className={cn(
                          buttonVariants({ variant: "outline" }),
                          "w-80 justify-center",
                        )}
                      >
                        検索
                      </Link>
                      <Button
                        variant="outline"
                        onClick={() => setDeleteTopicId(topic.id)}
                      >
                        削除
                      </Button>
                    </div>
                  </li>
                ))}
            </ul>
          )}
        </ScrollArea>
        <div className="flex justify-end">
          <CreateMessageTopicButton />
        </div>
      </CardContent>
      {deleteTopicId && (
        <AlertDialog
          open={!!deleteTopicId}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setDeleteTopicId(null);
            }
            setDeleteConfirmed(false);
          }}
        >
          <AlertDialogContent>
            <Form action={submitAction}>
              <input type="hidden" name="programId" value={programId} />
              <input
                type="hidden"
                name="messageTopicId"
                value={deleteTopicId}
              />
              <AlertDialogHeader>
                <AlertDialogTitle>メッセージトピックの削除</AlertDialogTitle>
                <AlertDialogDescription className="mt-4 space-y-3 text-gray-700">
                  本当にメッセージトピック 「{currentTopicName}」
                  を削除しますか？
                  <br />
                  この操作は元に戻せません
                </AlertDialogDescription>
                <div className="rounded-md border border-amber-200 bg-amber-50 p-4 dark:bg-amber-950">
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="delete-message-topic"
                      checked={deleteConfirmed}
                      onCheckedChange={(v) => setDeleteConfirmed(!!v)}
                      className="mt-0.5"
                    />
                    <label
                      htmlFor="delete-message-topic"
                      className="cursor-pointer text-sm leading-relaxed text-amber-800 dark:text-amber-300"
                    >
                      トピックに投票されたメッセージのデータも同時に削除されることを確認しました
                    </label>
                  </div>
                </div>
              </AlertDialogHeader>
              <AlertDialogFooter className="mt-6">
                <AlertDialogCancel
                  onClick={() => setDeleteTopicId(null)}
                  type="button"
                >
                  キャンセル
                </AlertDialogCancel>
                <Button
                  type="submit"
                  variant="destructive"
                  disabled={isPending || !deleteConfirmed}
                >
                  {isPending ? "削除中..." : "削除"}
                </Button>
              </AlertDialogFooter>
            </Form>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </Card>
  );
}
