"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useParams } from "next/navigation";
import { useActionState, useState } from "react";
import Form from "next/form";
import { saveUserPermissionAction } from "./actions";
import { UserData, UserSearchInput } from "./user-search-input";
import { ProgramPermission } from "@/adapter/dynamodb/program-permission";
import { ScrollArea } from "@/components/ui/scroll-area";
import { XIcon } from "lucide-react";

export interface EditUserPermissionProps {
  permissions: ProgramPermission[];
  permittedUsers: UserData[];
}

export function EditUserPermissionButton(props: EditUserPermissionProps) {
  const { programId } = useParams<{ programId: string }>();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<UserData[]>(
    props.permittedUsers,
  );
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      const result = await saveUserPermissionAction(formData);
      if (result === "ok") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>番組担当者を編集する</Button>
      <Form id="edit-user-permission" action={submitAction}>
        <Dialog
          open={isOpen}
          onOpenChange={(open) => {
            if (!open) {
              // モーダルを閉じても状態が保持されるので
              // モーダルの閉じたエフェクトが終わったくらいに初期化する
              window.setTimeout(() => {
                setSelectedUsers(props.permittedUsers);
              }, 200);
            }
            setIsOpen(open);
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>番組担当者を編集する</DialogTitle>
              <DialogDescription>
                この番組を表示・編集する権限を持つユーザーを選択してください
              </DialogDescription>
            </DialogHeader>
            <input
              type="hidden"
              id="programId"
              name="programId"
              form="edit-user-permission"
              value={programId}
            />
            <div>
              <UserSearchInput
                selectedUserIds={selectedUsers.map((u) => u.ID)}
                onSelectUser={(user) =>
                  setSelectedUsers((prev) => [...prev, user])
                }
              />
            </div>
            <div>
              <ScrollArea className="h-[240px] rounded-md border dark:border-gray-500">
                {selectedUsers.length === 0 && (
                  <div>
                    <p className="p-4">選択されているユーザーはいません</p>
                  </div>
                )}
                {selectedUsers.map((user) => (
                  <div
                    key={user.ID}
                    className="relative border px-4 py-2 dark:border-gray-500"
                  >
                    <p>{user.name || user.ID}</p>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="absolute right-0 top-0 h-full px-3 py-2 text-gray-400"
                      onClick={() =>
                        setSelectedUsers((prev) =>
                          prev.filter((u) => u.ID !== user.ID),
                        )
                      }
                    >
                      <XIcon className="h-4 w-4" />
                    </Button>
                    <input
                      type="hidden"
                      form="edit-user-permission"
                      id={user.ID}
                      name={user.ID}
                      value="true"
                    />
                  </div>
                ))}
              </ScrollArea>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                form="edit-user-permission"
                variant="default"
                disabled={isPending}
              >
                保存する
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </Form>
    </>
  );
}
