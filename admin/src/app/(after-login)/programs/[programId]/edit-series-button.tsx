"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useEffect, useState } from "react";
import Form from "next/form";
import { updateProgramScaleAction } from "./actions";
import { Program } from "@/adapter/dynamodb/program";
import { SeriesSearchInput } from "./series-search-input";
import { ExternalLinkIcon, XIcon } from "lucide-react";
import { components } from "@/r7/schema";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export interface EditSeriesButtonProps {
  program: Program;
}

export function EditSeriesButton(props: EditSeriesButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [seriesId, setSeriesId] = useState(props.program.Data.scale?.seriesId);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      if (seriesId) {
        formData.append("seriesId", seriesId);
      }
      const result = await updateProgramScaleAction(formData);
      if (result === "ok") {
        setIsOpen(false);
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>番組表API連携を編集する</Button>
      <Dialog open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <DialogContent className="w-[640px]">
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>番組表API連携を編集する</DialogTitle>
              <DialogDescription>
                番組表APIと連携し、設定したシリーズの双方向放送回で指定の想定最大RPS、前後のマージン時間でサーバースケール予約を自動で作成します
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <input
                type="hidden"
                id="programId"
                name="programId"
                value={props.program.ID}
              />
              <input
                type="hidden"
                id="programName"
                name="programName"
                value={props.program.Data.name}
              />
              <input
                type="hidden"
                id="expiresAt"
                name="expiresAt"
                value={props.program.Data.expiresAt}
              />
              <div className="flex items-center">
                <SeriesView seriesId={seriesId} />
                {seriesId && (
                  <Button
                    variant="ghost"
                    onClick={() => setSeriesId(undefined)}
                    className="px-3"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div>
                <SeriesSearchInput
                  onSelectSeries={(series) => setSeriesId(series?.id)}
                />
              </div>
              {seriesId && (
                <>
                  <div>
                    <Label htmlFor="expectedMaxRps">想定最大RPS</Label>
                    <Input
                      id="expectedMaxRps"
                      name="expectedMaxRps"
                      type="number"
                      step="10000"
                      min="10000"
                      max="200000"
                      defaultValue={
                        props.program.Data.scale?.expectedMaxRps ?? 10000
                      }
                    />
                  </div>
                  <div>
                    <Label htmlFor="marginHour">
                      放送前後のスケール変更のマージン (時間)
                    </Label>
                    <Input
                      id="marginHour"
                      name="marginHour"
                      type="number"
                      step="1"
                      min="1"
                      defaultValue={props.program.Data.scale?.marginHour ?? 1}
                    />
                  </div>
                </>
              )}
            </div>
            <DialogFooter className="mt-4">
              <Button type="submit" variant="default" disabled={isPending}>
                保存する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export function SeriesView(props: { seriesId: string | undefined }) {
  const [series, setSeries] = useState<
    components["schemas"]["TVSeries"] | null
  >(null);

  useEffect(() => {
    if (!props.seriesId) {
      setSeries(null);
      return;
    }

    fetchSeries(props.seriesId).then((series) => {
      setSeries(series ?? null);
    });
  }, [props.seriesId]);

  if (!props.seriesId) {
    return <div>番組表APIと連携していません</div>;
  }

  if (!series) {
    return <div>シリーズが見つかりません</div>;
  }

  return (
    <div className="w-full space-y-2">
      <div className="flex w-full justify-start gap-6">
        <div>
          <p className="text-sm">連携シリーズ</p>
          <p>{series.name}</p>
        </div>
        <div>
          <p className="text-sm">シリーズID</p>
          <p>{series.id}</p>
        </div>
      </div>
      {series.canonical ? (
        <a
          className="flex items-center gap-1 text-sm text-blue-500 underline"
          href={series.canonical}
          target="_blank"
          rel="noopener noreferrer"
        >
          {series.canonical}
          <ExternalLinkIcon className="h-4 w-4" />
        </a>
      ) : (
        <p className="text-sm text-gray-500">
          シリーズのウェブサイトが存在しません
        </p>
      )}
    </div>
  );
}

async function fetchSeries(
  seriesId: string,
): Promise<components["schemas"]["TVSeries"] | undefined> {
  const res = await fetch(
    `https://api.nhk.jp/r7/t/tvseries/ts/${seriesId}.json`,
  );

  if (res.status === 404) {
    return undefined;
  }

  return res.json();
}
