"use client";

import { useActionState, useState } from "react";
import Form from "next/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { createAggAPIAccessTokenAction } from "./actions";

export function CreateAggAPIAccessTokenButton({
  programId,
}: {
  programId: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      await createAggAPIAccessTokenAction(formData);
      setIsOpen(false);
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        集計APIのアクセス権を作成する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>集計APIのアクセス権を作成する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <input type="hidden" name="programId" value={programId} />
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  名前
                </Label>
                <Input id="name" name="name" required className="col-span-3" />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" variant="default" disabled={isPending}>
                作成する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
