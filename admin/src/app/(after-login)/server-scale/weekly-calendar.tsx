import { Card, CardContent } from "@/components/ui/card";
import { Scale } from "@/adapter/dynamodb/scale";
import { ScaleSchedule } from "@/adapter/dynamodb/scale-schedule";
import { Program } from "@/adapter/dynamodb/program";
import { components } from "@/r7/schema";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import weekday from "dayjs/plugin/weekday";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isBetween from "dayjs/plugin/isBetween";
import "dayjs/locale/ja";

dayjs.extend(localizedFormat);
dayjs.extend(weekday);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(isBetween);
dayjs.locale("ja");

interface WeeklyScheduleProps {
  schedules: ScaleSchedule[];
  scales: Scale[];
  programs: Program[];
  seriesBroadcasts: Array<
    readonly [Program, components["schemas"]["BroadcastEvent"]]
  >;
}

export function WeeklyCalendar(props: WeeklyScheduleProps) {
  const weekStart = dayjs();
  const days = Array.from({ length: 7 }, (_, i) => weekStart.add(i, "day"));

  return (
    <div className="w-full overflow-x-auto text-sm">
      <div className="flex items-center justify-end gap-6">
        <h4>凡例:</h4>
        <div className="flex items-center gap-2">
          <div className="h-8 w-12 rounded border border-4 border-red-500 bg-red-300 dark:bg-red-700"></div>
          <div>サーバースケール稼働中の予約</div>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-8 w-12 rounded bg-red-300 dark:bg-red-700"></div>
          <div>確定した予約</div>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-8 w-12 rounded bg-green-300 dark:bg-green-700"></div>
          <div>未確定の予約</div>
        </div>
      </div>
      <div className="min-w-[1024px]">
        <div className="my-4 grid grid-cols-7 gap-2">
          {days.map((day, index) => (
            <div key={index} className="text-center font-semibold">
              <div>{day.format("M/D")}</div>
              <div className="text-sm text-gray-500">{day.format("ddd")}</div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-2">
          {days.map((day, index) => (
            <Card key={index} className="min-h-[200px]">
              <CardContent className="space-y-1 p-2">
                {getEventsForDay(day, props).map((event) => (
                  <EventItem key={event.id} event={event} />
                ))}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

type Event = {
  id: string;
  kind: "spot" | "schedule" | "program";
  source?: "schedule" | "program";
  programId: string;
  sourceProgramId?: string;
  title: string;
  programName: string;
  start: dayjs.Dayjs;
  end: dayjs.Dayjs;
  isActive: boolean;
};

function getEventsForDay(
  day: dayjs.Dayjs,
  props: WeeklyScheduleProps,
): Event[] {
  const now = dayjs();
  const startDay = day.startOf("day");
  const endDay = day.endOf("day");
  const weekday = getWeekday(day);

  const scaleEvents: Event[] = props.scales.map((s) => {
    const start = dayjs(s.StartTime);
    const end = dayjs(s.EndTime);
    const source = s.Data.source == null ? undefined : s.Data.source.kind;

    return {
      id: `scale-${s.ID}`,
      kind: "spot",
      source,
      programId: s.Data.programId,
      sourceProgramId: s.Data.source != null ? s.Data.programId : undefined,
      title: s.Data.name,
      programName:
        props.programs.find((p) => p.ID === s.Data.programId)?.Data.name ??
        "--",
      start,
      end,
      isActive: now.isBetween(start, end),
    };
  });

  const scheduleEvents: Event[] = props.schedules
    .filter((s) => s.Data.weekDays.includes(weekday))
    .map((s) => {
      const start = dayjs(day.format("YYYY-MM-DD") + " " + s.Data.startAt);
      const end = dayjs(day.format("YYYY-MM-DD") + " " + s.Data.endAt);

      return {
        id: `schedule-${s.ID}`,
        kind: "schedule",
        programId: s.Data.programId,
        title: s.Data.name,
        programName:
          props.programs.find((p) => p.ID === s.Data.programId)?.Data.name ??
          "--",
        start,
        end,
        isActive: now.isBetween(start, end),
      };
    });

  const broadcastEvents: Event[] = props.seriesBroadcasts.map(
    ([program, b]) => {
      const start = dayjs(b.startDate!);
      const end = dayjs(b.endDate!);

      return {
        id: `broadcast-${program.ID}-${b.id}`,
        kind: "program",
        programId: program.ID,
        title: b.name,
        programName: program.Data.name,
        start,
        end,
        isActive: now.isBetween(start, end),
      };
    },
  );

  return (
    [...scaleEvents, ...scheduleEvents, ...broadcastEvents]
      .filter(
        (e) =>
          e.start.isSameOrAfter(startDay) && e.start.isSameOrBefore(endDay),
      )
      // 今日の予定で、番組表API連携と繰り返し予約で、それに対応するスポット予約がある場合は表示しない
      .filter((e) => {
        if (!e.start.isSame(now, "day")) {
          return true;
        }

        switch (e.kind) {
          case "spot":
            return true;
          case "program":
            return !scaleEvents.some(
              (se) => se.sourceProgramId === e.programId,
            );
          case "schedule":
            return !scaleEvents.some(
              (se) => se.sourceProgramId === e.programId,
            );
          default:
            return true;
        }
      })
      .sort((a, b) => a.start.valueOf() - b.start.valueOf())
  );
}

function getWeekday(day: dayjs.Dayjs): string {
  const weekdays = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
  return weekdays[day.weekday()];
}

function EventItem({ event }: { event: Event }) {
  // 開始と終了で日付が異なる場合終わりの時刻に日付を追加する
  const end = event.start.isSame(event.end, "day")
    ? event.end.format("HH:mm")
    : event.end.format("M/D HH:mm");

  return (
    <div
      className={cn(
        "flex flex-col gap-0.5 rounded border-4 p-1 text-sm",
        getBg(event.kind),
        getBorder(event),
      )}
    >
      <div className="text-xs">{kindText(event)}</div>
      <div className="font-semibold">{event.title}</div>
      <div className="text-xs">{event.programName}</div>
      <div className="text-xs">
        {event.start.format("HH:mm")} - {end}
      </div>
    </div>
  );
}

function getBg(kind: Event["kind"]): string {
  switch (kind) {
    case "spot":
      return "bg-red-300 dark:bg-red-700";
    case "schedule":
    case "program":
      return "bg-green-300 dark:bg-green-700";
    default:
      throw new Error("Unknown kind: " + kind);
  }
}

function getBorder(event: Event): string {
  switch (event.kind) {
    case "spot":
      return event.isActive
        ? "border-red-500"
        : "border-red-300 dark:border-red-700";
    case "schedule":
    case "program":
      return "border-green-300 dark:border-green-700";
    default:
      throw new Error("Unknown kind: " + event.kind);
  }
}

function kindText(event: Event): string {
  switch (event.kind) {
    case "spot":
      return "確定予約";
    case "schedule":
      return "繰り返し予約";
    case "program":
      return "番組表API連携";
    default:
      throw new Error("Unknown kind: " + event.kind);
  }
}
