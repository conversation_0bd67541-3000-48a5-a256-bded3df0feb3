"use client";

import { Button } from "@/components/ui/button";
import { createScaleByScheduleAction } from "./actions";
import Form from "next/form";
import { useActionState } from "react";

export function CreateScaleByScheduleButton() {
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      await createScaleByScheduleAction();
    },
    {},
  );

  return (
    <Form action={submitAction}>
      <Button type="submit" disabled={isPending}>
        繰り返し予約による本日分の確定予約を作成する
      </Button>
    </Form>
  );
}
