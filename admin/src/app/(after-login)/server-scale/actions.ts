"use server";

import { createScaleBySchedule } from "@/adapter/lambda/helper-api";
import { getLoginUser } from "@/auth";
import { revalidatePath } from "next/cache";

export async function revalidateServerScalePageAction() {
  revalidatePath("/server-scale");
}

export async function createScaleByScheduleAction() {
  const loginUser = await getLoginUser();
  if (loginUser.isAdmin) {
    await createScaleBySchedule();
  }
}
