import { PageTitle } from "@/components/page-title";
import { fetchScalesAfter } from "@/adapter/dynamodb/scale";
import dayjs from "dayjs";
import { SeriesScaleSchedules } from "./series-scale-schedules";
import {
  ScaleSchedule,
  fetchScaleSchedules,
} from "@/adapter/dynamodb/scale-schedule";
import { getLoginUser, LoginUser } from "@/auth";
import { Program, fetchPrograms } from "@/adapter/dynamodb/program";
import { WeeklyCalendar } from "./weekly-calendar";
import { fetchComingSeriesBroadcasts } from "@/r7/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ServiceStatus, ServiceStatusDetail } from "./server-status";
import { RefreshServerStatusButton } from "./refresh-server-status-button";
import { CreateScaleByScheduleButton } from "./create-scale-by-schedule-button";
import {
  UpcomingScales,
  WeekScaleSchedules,
} from "@/components/server-scale/server-components";

export default async function ServerScalePage() {
  const loginUser = await getLoginUser();
  const [schedules, upcomingScales, programs] = await Promise.all([
    fetchSchedulesByUser(loginUser),
    fetchUpcomingScalesByPermission(loginUser),
    fetchProgramsByUser(loginUser),
  ]);

  const programsWithSchedule = programs.filter(
    (program) => program.Data.scale != null,
  );

  const seriesBroadcasts = (
    await Promise.all(
      programsWithSchedule.map((program) =>
        fetchComingSeriesBroadcasts(program.Data.scale!.seriesId).then(
          (res) =>
            res?.result
              .filter((be) => be.misc?.isInteractive)
              .map((r) => [program, r] as const) ?? [],
        ),
      ),
    )
  ).flat();

  return (
    <div className="flex flex-col gap-4">
      <PageTitle>サーバースケール管理</PageTitle>
      {loginUser.isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle>
              サーバーステータス詳細
              <span className="ml-2 text-base">(管理者のみ)</span>
            </CardTitle>
            <CardDescription></CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <ServiceStatusDetail />
          </CardContent>
        </Card>
      )}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            サーバーステータス <RefreshServerStatusButton />
          </CardTitle>
          <CardDescription>
            他の番組が同時刻にサーバースケール予約をしている場合、その分も加算して表示されます
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ServiceStatus />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>週間スケジュール</CardTitle>
          <CardDescription></CardDescription>
        </CardHeader>
        <CardContent>
          <WeeklyCalendar
            schedules={schedules}
            scales={upcomingScales}
            seriesBroadcasts={seriesBroadcasts}
            programs={programs}
          />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>確定予約一覧</CardTitle>
          <CardDescription>
            スケール変更が開始して完了するまで5~10分程度かかります。
            それを考慮して開始・終了時刻を設定してください。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UpcomingScales />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>繰り返し予約一覧</CardTitle>
          <CardDescription>
            繰り返し予約は毎朝4時に当日分が確定予約になります。当日に作成された予約は、確定予約にはなりません。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <WeekScaleSchedules />
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>番組表API予約一覧</CardTitle>
          <CardDescription>
            番組表APIと連携して予約を作成します。設定したシリーズの双方向フラグを持つ放送回が予約の対象になります。
            <br />
            番組表API予約は毎朝4時に当日分が確定予約になります。当日に作成された予約は、確定予約にはなりません。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SeriesScaleSchedules programsWithSchedule={programsWithSchedule} />
        </CardContent>
      </Card>
      {/* 繰り返し予約を確定予約にするボタン */}
      {false && <CreateScaleByScheduleButton />}
    </div>
  );
}

// 今日以降のスケールを取得
async function fetchUpcomingScalesByPermission(loginUser: LoginUser) {
  const today = dayjs().hour(0).minute(0).second(0).millisecond(0);
  const scales = await fetchScalesAfter(today);

  if (loginUser.isAdmin) {
    return scales;
  }

  return scales.filter((s) =>
    loginUser.permissions.some((p) => p.ID === s.Data.programId),
  );
}

async function fetchSchedulesByUser(
  loginUser: LoginUser,
): Promise<ScaleSchedule[]> {
  let schedules = await fetchScaleSchedules();

  if (!loginUser.isAdmin) {
    schedules = schedules.filter((schedule) =>
      loginUser.permissions.some(
        (permission) => permission.ID === schedule.Data.programId,
      ),
    );
  }

  return schedules;
}

async function fetchProgramsByUser(loginUser: LoginUser): Promise<Program[]> {
  const programs = await fetchPrograms();

  if (loginUser.isAdmin) {
    return programs;
  }

  return programs.filter((program) =>
    loginUser.permissions.some((permission) => permission.ID === program.ID),
  );
}
