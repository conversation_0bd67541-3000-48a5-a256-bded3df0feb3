import { fetchCurrentStatus } from "@/adapter/lambda/helper-api";
import { Badge } from "@/components/ui/badge";
import { CircleAlert } from "lucide-react";

export async function ServiceStatusDetail() {
  const statuses = await fetchCurrentStatus();

  return (
    <div className="flex gap-4">
      {statuses.map((s, i) => (
        <div
          key={s.serviceName}
          className="w-full rounded-md bg-gray-100 p-4 dark:bg-gray-800"
        >
          <h3>
            AZ {i + 1}{" "}
            {s.taskDefinition.includes("Small") ? (
              <Badge variant="outline">small</Badge>
            ) : (
              <Badge variant="destructive">large</Badge>
            )}
          </h3>
          <p>
            {s.runningCount} 台 / {s.desiredCount} 台稼働中、
            {s.pendingCount} 台保留中
          </p>
        </div>
      ))}
    </div>
  );
}

export async function ServiceStatus() {
  const statuses = await fetchCurrentStatus();
  const isScaling = statuses.some((s) => s.desiredCount > s.runningCount);
  // 3 AZ分あるECSの状態のうち最初の一つのものを使う
  const status = statuses[0];
  const isNormal =
    status.taskDefinition.includes("Small") && status.desiredCount === 1;

  return (
    <div>
      <div className="w-full rounded-lg bg-gray-100 py-12 text-center dark:bg-gray-800">
        <p className="text-2xl font-extrabold">
          {isNormal ? "通常稼働中" : "スケール稼働中"}
        </p>
        <p>最大毎秒投票数: {isNormal ? "3000" : status.desiredCount * 10000}</p>
        {isScaling && (
          <p className="flex items-center justify-center gap-1 text-red-500">
            <CircleAlert className="h-4 w-4" />
            <span>スケールの変更を実行中です</span>
          </p>
        )}
      </div>
    </div>
  );
}
