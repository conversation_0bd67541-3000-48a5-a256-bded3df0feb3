import { Program } from "@/adapter/dynamodb/program";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { fetchComingSeriesBroadcasts, fetchSeries } from "@/r7/client";
import { components } from "@/r7/schema";
import dayjs from "dayjs";
import { LinkIcon } from "lucide-react";
import Link from "next/link";

export async function SeriesScaleSchedules(props: {
  programsWithSchedule: Program[];
}) {
  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>番組名</TableHead>
            <TableHead>シリーズ</TableHead>
            <TableHead>想定最大RPS</TableHead>
            <TableHead>前後マージン時間</TableHead>
            <TableHead>次回の予約</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {props.programsWithSchedule.map((program) => (
            <ProgramTableRow key={program.ID} program={program} />
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

async function ProgramTableRow({ program }: { program: Program }) {
  const [series, broadcasts] = await Promise.all([
    fetchSeries(program.Data.scale!.seriesId),
    fetchComingSeriesBroadcasts(program.Data.scale!.seriesId),
  ]);

  const nextBe = broadcasts?.result.find((be) => be.misc?.isInteractive);

  return (
    <TableRow>
      <TableCell>{program.Data.name}</TableCell>
      <TableCell>
        {series
          ? `${series.name} (${series.id})`
          : `シリーズ(ID: ${program.Data.scale!.seriesId})が見つかりません`}
      </TableCell>
      <TableCell>{program.Data.scale!.expectedMaxRps}</TableCell>
      <TableCell>{program.Data.scale!.marginHour}</TableCell>
      <TableCell>{nextBe && <BroadcastEventCell be={nextBe} />}</TableCell>
      <TableCell className="flex justify-end">
        <Link
          href={`/programs/${program.ID}`}
          className="flex items-center gap-2"
        >
          番組詳細
          <LinkIcon className="h-4 w-4" />
        </Link>
      </TableCell>
    </TableRow>
  );
}

function BroadcastEventCell({
  be,
}: {
  be: components["schemas"]["BroadcastEvent"];
}) {
  const from = dayjs(be.startDate).tz("Asia/Tokyo").format("YYYY/MM/DD HH:mm");
  const end = dayjs(be.endDate).tz("Asia/Tokyo").format("HH:mm");

  return (
    <div className="text-xs">
      <p>{be.name}</p>
      <p>
        {from} ~ {end}
      </p>
    </div>
  );
}
