"use client";

import { But<PERSON> } from "@/components/ui/button";
import { revalidateServerScalePageAction } from "./actions";
import { useActionState } from "react";
import Form from "next/form";
import { Loader2 } from "lucide-react";

export function RefreshServerStatusButton() {
  const [_, submitAction, isPending] = useActionState(async () => {
    await revalidateServerScalePageAction();
  }, undefined);

  return (
    <Form action={submitAction}>
      <Button variant="secondary" disabled={isPending}>
        更新
        {isPending && <Loader2 className="animate-spin" />}
      </Button>
    </Form>
  );
}
