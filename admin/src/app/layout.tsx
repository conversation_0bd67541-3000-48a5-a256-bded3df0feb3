import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import {
  KeyRoundIcon,
  LogsIcon,
  ServerIcon,
  StickyNoteIcon,
  UserIcon,
  Users,
  Vote,
} from "lucide-react";
import { ModeToggle } from "@/components/mode-toggle-button";
import Link from "next/link";
import { SignOutButton } from "@/components/signout-button";
import { Toaster } from "@/components/ui/toaster";
import { FlashMessage } from "@/components/flash-message";
import { MenuLink } from "@/components/menu-link";
import { environment } from "@/environments";
import { getLoginUser, LoginUser } from "@/auth";
import { ManualDropdown } from "@/components/manual-dropdown";
import { cn } from "@/lib/utils";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "投票基盤管理",
  description: "投票基盤の管理画面",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  let loginUser: LoginUser | null = null;
  try {
    loginUser = await getLoginUser();
  } catch (error) {
    // ログインしていない場合でもエラーにならないようにする
    console.error(error);
  }

  return (
    <html lang="ja" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <header
            className={cn(
              "sticky top-0 z-10 flex h-[72px] items-center justify-between bg-gray-100 px-4 py-3 dark:bg-gray-800 md:px-6 md:py-4",
              environment.envName === "Dev" && "bg-pink-100 dark:bg-pink-950",
            )}
          >
            <div className="flex items-center gap-2">
              <Link
                className="flex items-center gap-2 text-xl font-semibold"
                href="/"
                prefetch={false}
              >
                <Vote className="h-8 w-8 pr-2" />
                Vear管理画面
              </Link>
            </div>
            <div className="flex items-center gap-2">
              <ManualDropdown isAdmin={loginUser?.isAdmin ?? false} />
              {loginUser && (
                <>
                  <ModeToggle />
                  <SignOutButton />
                </>
              )}
            </div>
          </header>
          <div className="flex p-2">
            {loginUser && <SideMenu loginUser={loginUser} />}
            <div className="w-full p-4">{children}</div>
          </div>
          <Toaster />
          <FlashMessage />
        </ThemeProvider>
      </body>
    </html>
  );
}

const SideMenu = (props: { loginUser: LoginUser }) => {
  return (
    <div className="bg-background sticky top-20 z-10 flex h-full w-64 flex-col p-4">
      <div className="mb-4 flex items-center gap-2">
        <UserIcon className="h-5 w-5" />
        <div className="text-sm font-medium">
          {props.loginUser.session.user?.email}
        </div>
      </div>
      <div className="flex flex-col gap-8">
        <div className="flex flex-col">
          <MenuLink href="/programs">
            <StickyNoteIcon className="mr-2 h-4 w-4" />
            番組管理
          </MenuLink>
          <MenuLink href="/server-scale">
            <ServerIcon className="mr-2 h-4 w-4" />
            サーバースケール管理
          </MenuLink>
          <MenuLink href="/agg-api/permissions">
            <KeyRoundIcon className="mr-2 h-4 w-4" />
            集計APIアクセス管理
          </MenuLink>
        </div>
        {props.loginUser.isAdmin ? (
          <div className="mt-auto flex flex-col">
            <p className="mb-2 text-xs">管理者向け</p>
            <MenuLink href="/users">
              <Users className="mr-2 h-4 w-4" />
              ユーザー管理
            </MenuLink>
            <MenuLink href="/logs">
              <LogsIcon className="mr-2 h-4 w-4" />
              操作履歴
            </MenuLink>
          </div>
        ) : null}
      </div>
    </div>
  );
};
