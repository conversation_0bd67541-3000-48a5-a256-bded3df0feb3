import { authOptions, providers } from "@/auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { NdciSignInButton } from "./ndci-sign-in-button";
import { BackupAdminSignInForm } from "./backup-admin-sign-in-form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default async function signInPage() {
  const session = await getServerSession(authOptions);
  if (session) {
    return redirect("/");
  }

  const idNdciAvailable = providers.some((p) => p.id === "keycloak");

  return (
    <div className="mx-auto max-w-2xl space-y-8 rounded border p-8">
      <h2 className="text-xl font-bold">ログイン</h2>
      {idNdciAvailable && <NdciSignInButton />}
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1">
          <AccordionTrigger className="text-gray-500">
            緊急用管理者ログイン
          </AccordionTrigger>
          <AccordionContent className="m-2">
            <BackupAdminSignInForm />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
