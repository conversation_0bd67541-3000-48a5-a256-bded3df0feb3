"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { signIn } from "next-auth/react";
import { useState } from "react";

export function BackupAdminSignInForm() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");

  return (
    <form
      onSubmit={(ev) => {
        ev.preventDefault();
        signIn("credentials", { username, password });
      }}
      className="space-y-4"
    >
      <div>
        <Label htmlFor="username">ユーザー名</Label>
        <Input
          id="username"
          name="username"
          type="text"
          value={username}
          onChange={(ev) => setUsername(ev.target.value)}
        />
      </div>
      <div>
        <Label htmlFor="password">パスワード</Label>
        <Input
          id="password"
          name="password"
          type="password"
          value={password}
          onChange={(ev) => setPassword(ev.target.value)}
        />
      </div>
      <div className="flex justify-end">
        <Button type="submit" variant="secondary">
          緊急用管理者アカウントでログインする
        </Button>
      </div>
    </form>
  );
}
