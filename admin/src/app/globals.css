@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --chart-6: 340 70% 60%;
    --chart-7: 120 60% 50%;
    --chart-8: 200 80% 70%;
    --chart-9: 50 90% 60%;
    --chart-10: 280 60% 65%;
    --chart-11: 10 80% 55%;
    --chart-12: 90 70% 50%;
    --chart-13: 210 60% 55%;
    --chart-14: 30 90% 65%;
    --chart-15: 160 80% 60%;
    --chart-16: 0 0% 50%;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --chart-6: 60 70% 60%;
    --chart-7: 100 60% 50%;
    --chart-8: 180 80% 70%;
    --chart-9: 260 90% 60%;
    --chart-10: 320 60% 65%;
    --chart-11: 20 80% 55%;
    --chart-12: 80 70% 50%;
    --chart-13: 140 60% 55%;
    --chart-14: 200 90% 65%;
    --chart-15: 240 80% 60%;
    --chart-16: 0 0% 60%;
  }
}
