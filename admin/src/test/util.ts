import { LocalstackContainer } from "@testcontainers/localstack";
import { CreateTableCommand, DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { vi, afterAll } from "vitest";

export function mockDynamoDB() {
  // DynamoDBクライアントの向き先をLocalstackに変更する
  vi.mock(import("../adapter/dynamodb/client"), async (importOriginal) => {
    const mod = await importOriginal();
    const { client, container } = await createDynamoDBClient();
    const docClient = DynamoDBDocumentClient.from(client);

    afterAll(async () => {
      await container.stop();
    });

    return {
      ...mod,
      client,
      docClient,
    };
  });

  // environmentsの必要な値を設定する
  vi.mock(import("../environments"), async (importOriginal) => {
    const mod = await importOriginal();
    return {
      ...mod,
      environment: {
        ...mod.environment,
        voteTableName: "Vote",
        voteTableIdTypeIndexName: "Index-ID-Type",
        cmsTableName: "CMS",
        cmsTableIdTypeIndexName: "Index-ID-Type",
        cmsTableUserIdIdIndexName: "Index-UserId-Id",
        cmsTableTypeEndTimeIndexName: "Index-Type-EndTime",
      },
    };
  });
}

export async function createDynamoDBClient() {
  const container = await new LocalstackContainer().start();

  const client = new DynamoDBClient({
    endpoint: container.getConnectionUri(),
    region: "ap-northeast-1",
    credentials: {
      secretAccessKey: "test",
      accessKeyId: "test",
    },
  });

  const pt = {
    ReadCapacityUnits: 5,
    WriteCapacityUnits: 5,
  };
  const pj = {
    ProjectionType: "ALL",
  } as const;

  const createVoteTableCommand = new CreateTableCommand({
    TableName: "Vote",
    AttributeDefinitions: [
      {
        AttributeName: "Type",
        AttributeType: "S",
      },
      {
        AttributeName: "ID",
        AttributeType: "S",
      },
      {
        AttributeName: "EndTime",
        AttributeType: "S",
      },
      {
        AttributeName: "UserId",
        AttributeType: "S",
      },
    ],
    KeySchema: [
      {
        AttributeName: "Type",
        KeyType: "HASH",
      },
      {
        AttributeName: "ID",
        KeyType: "RANGE",
      },
    ],
    ProvisionedThroughput: pt,
    GlobalSecondaryIndexes: [
      {
        IndexName: "Index-ID-Type",
        KeySchema: [
          {
            AttributeName: "ID",
            KeyType: "HASH",
          },
          {
            AttributeName: "Type",
            KeyType: "RANGE",
          },
        ],
        Projection: pj,
        ProvisionedThroughput: pt,
      },
    ],
  });

  const createVoteTableResponse = await client.send(createVoteTableCommand);
  if (createVoteTableResponse.$metadata.httpStatusCode !== 200) {
    throw new Error("Failed to create Vote table");
  }

  const createCmsTableCommand = new CreateTableCommand({
    TableName: "CMS",
    AttributeDefinitions: [
      {
        AttributeName: "Type",
        AttributeType: "S",
      },
      {
        AttributeName: "ID",
        AttributeType: "S",
      },
      {
        AttributeName: "EndTime",
        AttributeType: "S",
      },
      {
        AttributeName: "UserId",
        AttributeType: "S",
      },
    ],
    KeySchema: [
      {
        AttributeName: "Type",
        KeyType: "HASH",
      },
      {
        AttributeName: "ID",
        KeyType: "RANGE",
      },
    ],
    ProvisionedThroughput: pt,
    GlobalSecondaryIndexes: [
      {
        IndexName: "Index-ID-Type",
        KeySchema: [
          {
            AttributeName: "ID",
            KeyType: "HASH",
          },
          {
            AttributeName: "Type",
            KeyType: "RANGE",
          },
        ],
        Projection: pj,
        ProvisionedThroughput: pt,
      },
      {
        IndexName: "Index-Type-EndTime",
        KeySchema: [
          {
            AttributeName: "Type",
            KeyType: "HASH",
          },
          {
            AttributeName: "EndTime",
            KeyType: "RANGE",
          },
        ],
        Projection: pj,
        ProvisionedThroughput: pt,
      },
      {
        IndexName: "Index-UserId-Id",
        KeySchema: [
          {
            AttributeName: "UserId",
            KeyType: "HASH",
          },
          {
            AttributeName: "ID",
            KeyType: "RANGE",
          },
        ],
        Projection: pj,
        ProvisionedThroughput: pt,
      },
    ],
  });

  const createCmsTableResponse = await client.send(createCmsTableCommand);
  if (createCmsTableResponse.$metadata.httpStatusCode !== 200) {
    throw new Error("Failed to create CMS table");
  }

  return { client, container };
}
