import type { ActionType } from "@/adapter/dynamodb/history";

export function logLabel(kind: ActionType["kind"]): string {
  let label: string;
  switch (kind) {
    case "login":
      label = "ログイン";
      break;
    case "logout":
      label = "ログアウト";
      break;
    case "program#create":
      label = "番組作成";
      break;
    case "program#update-name":
      label = "番組名更新";
      break;
    case "program#update-expires-at":
      label = "番組使用期限更新";
      break;
    case "program#create-message-topic":
      label = "番組メッセージトピック作成";
      break;
    case "program#delete-message-topic":
      label = "番組メッセージトピック削除";
      break;
    case "program#update-permission":
      label = "番組担当ユーザー更新";
      break;
    case "program#update-validation":
      label = "番組バリデーション更新";
      break;
    case "program#delete":
      label = "番組削除";
      break;
    case "program-scale#update":
      label = "番組表API連携更新";
      break;
    case "scale#create":
      label = "スポットスケール予約作成";
      break;
    case "scale#update":
      label = "スポットスケール予約更新";
      break;
    case "scale#delete":
      label = "スポットスケール予約削除";
      break;
    case "scale-schedule#create":
      label = "スケジュールスケール予約作成";
      break;
    case "scale-schedule#update":
      label = "スケジュールスケール予約更新";
      break;
    case "scale-schedule#delete":
      label = "スケジュールスケール予約削除";
      break;
    case "aggApiToken#create":
      label = "集計APIアクセストークン作成";
      break;
    case "aggApiToken#update-expires-at":
      label = "集計APIアクセストークン期限更新";
      break;
    case "aggApiToken#delete":
      label = "集計APIアクセストークン削除";
      break;
    case "admin#switch-user-role":
      label = "ユーザー権限切替";
      break;
    case "admin#update-user-note":
      label = "ユーザーメモ更新";
      break;
    case "admin#delete-user":
      label = "ユーザー削除";
      break;
    default:
      const _: never = kind;
      label = kind;
  }
  return label;
}
