import { environment } from "@/environments";
import { invokeLambda } from "./client";
import { fetchECSTaskIPs } from "../ecs/client";

export interface MessageItem {
  id: string;
  value: Record<string, string>;
}

export async function fetchMessages(
  programId: string,
  topicId: string,
  limit: number,
  cursor?: string,
): Promise<{
  messages: MessageItem[];
  cursor: string;
}> {
  const result = await invokeLambda(environment.counterVpcBridgeFunctionName!, {
    type: "list_messages",
    program_id: programId,
    topic_id: topicId,
    limit,
    cursor,
  });

  const data = JSON.parse(result.toString());
  if ("errorMessage" in data) {
    throw new Error(data.errorMessage);
  }

  return data;
}

export async function fetchMessagesCount(
  programId: string,
  topicId: string,
): Promise<{ count: number }> {
  const result = await invokeLambda(environment.counterVpcBridgeFunctionName!, {
    type: "count_messages",
    program_id: programId,
    topic_id: topicId,
  });

  const data = JSON.parse(result.toString());
  if ("errorMessage" in data) {
    throw new Error(data.errorMessage);
  }

  return data;
}

export async function resetCounterServerSettings() {
  const ipAddresses = await fetchECSTaskIPs();
  await invokeLambda(environment.counterVpcBridgeFunctionName!, {
    type: "reset_setting",
    ip_addresses: ipAddresses,
  });
}

export async function dumpMessages(
  programId: string,
  topicId: string,
): Promise<{
  programId: string;
  topicId: string;
  key: string;
}> {
  const result = await invokeLambda(environment.counterVpcBridgeFunctionName!, {
    type: "dump_messages",
    program_id: programId,
    topic_id: topicId,
  });

  const data = JSON.parse(result.toString());
  if ("errorMessage" in data) {
    throw new Error(data.errorMessage);
  }

  return data;
}

export async function clearMessages(
  programId: string,
  topicId: string,
): Promise<{
  programId: string;
  topicId: string;
  count: number;
}> {
  const result = await invokeLambda(environment.counterVpcBridgeFunctionName!, {
    type: "clear_messages",
    program_id: programId,
    topic_id: topicId,
  });

  const data = JSON.parse(result.toString());
  if ("errorMessage" in data) {
    throw new Error(data.errorMessage);
  }

  return data;
}
