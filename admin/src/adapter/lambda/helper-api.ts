import "server-only";
import { environment } from "@/environments";
import { invokeLambdaHTTPMethod, LambdaHTTPError } from "./client";

export interface ECSService {
  serviceName: string;
  taskDefinition: string;
  desiredCount: number;
  runningCount: number;
  pendingCount: number;
}

// serviceNameに含まれるAZ
export const availabilityZones = [
  "apnortheast1a",
  "apnortheast1c",
  "apnortheast1d",
];

export async function fetchCurrentStatus() {
  const result = await invokeLambdaHTTPMethod(
    environment.helperFunctionName!,
    "/api/v1/services",
    "GET",
  );

  return sortByAZ(result as ECSService[]);
}

export type ScaleExecutionResult =
  | {
      kind: "ok";
      services: ECSService[];
    }
  | {
      kind: "pipeline-in-progress";
    };

export async function executeAutoScale(): Promise<ScaleExecutionResult> {
  try {
    const result = await invokeLambdaHTTPMethod(
      environment.helperFunctionName!,
      "/api/v1/auto-scale",
      "POST",
    );

    return { kind: "ok", services: sortByAZ(result as ECSService[]) };
  } catch (e) {
    // /api/v1/auto-scaleが503を返す場合はデプロイしている最中
    if (e instanceof LambdaHTTPError && e.statusCode === 503) {
      return { kind: "pipeline-in-progress" };
    }

    throw e;
  }
}

interface VoteResult {
  choices: { [key: string]: number };
  az: string;
  options: string[];
}

export interface VoteGroupResult {
  // トピックIDごとの投票結果
  [key: string]: VoteResult;
}

export interface VoteResultBySecondItem {
  timestamp: number;
  choices: { [key: string]: number };
}

export interface VoteResultBySecond {
  // AZごとの投票結果
  choices: { [key: string]: VoteResultBySecondItem[] };
  options: string[];
}

export interface VoteResultsBySecond {
  // トピックIDごとの投票結果
  [key: string]: VoteResultBySecond;
}

export async function fetchVoteGroupResult(
  programId: string,
  groupId: string,
): Promise<VoteGroupResult> {
  const result = await invokeLambdaHTTPMethod<VoteGroupResult>(
    environment.helperFunctionName!,
    `/api/v1/rtc/${programId}/${groupId}`,
    "GET",
  );

  return result;
}

export async function fetchVoteGroupTimeSeriesResult(
  programId: string,
  groupId: string,
): Promise<VoteResultsBySecond> {
  const result = await invokeLambdaHTTPMethod<VoteResultsBySecond>(
    environment.helperFunctionName!,
    `/api/v1/rtc/${programId}/${groupId}/timeseries`,
    "GET",
  );

  return result;
}

export async function createScaleBySchedule() {
  const result = await invokeLambdaHTTPMethod<VoteGroupResult>(
    environment.helperFunctionName!,
    "/api/v1/create-scale-by-schedule",
    "POST",
  );

  return result;
}

function sortByAZ(services: ECSService[]): ECSService[] {
  return availabilityZones.map((az) => {
    const s = services.find((service) => service.serviceName.includes(az));
    if (s == null) {
      throw new Error(`Service for AZ ${az} not found`);
    }
    return s;
  });
}
