import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { environment } from "@/environments";
import { fromEnv, fromSSO } from "@aws-sdk/credential-providers";

const client = getClient();

function getClient() {
  if (environment.local) {
    return new LambdaClient({
      credentials: fromSSO(),
    });
  }

  return new LambdaClient({
    credentials: fromEnv(),
  });
}

export async function invokeLambda(
  funcName: string,
  body: any,
): Promise<Buffer> {
  const command = new InvokeCommand({
    FunctionName: funcName,
    Payload: JSON.stringify(body),
  });

  const { Payload } = await client.send(command);
  const result = Buffer.from(Payload!);
  return result;
}

export class LambdaHTTPError extends Error {
  constructor(
    public statusCode: number,
    public body: any,
    public message: string,
  ) {
    super(`HTTP Error: ${statusCode}, ${message}`);
  }
}

export async function invokeLambdaHTTPMethod<T>(
  funcName: string,
  path: string,
  method: "GET" | "POST",
): Promise<T> {
  const result = await invoke<PERSON>ambda(funcName, {
    version: "2.0",
    rawPath: path,
    requestContext: {
      http: {
        method,
        path: path,
        protocol: "HTTP/1.1",
      },
    },
  });

  const resultJson = JSON.parse(result.toString());
  if (resultJson.statusCode !== 200) {
    throw new LambdaHTTPError(
      resultJson.statusCode,
      resultJson,
      `Failed to fetch ${path}, result: ${JSON.stringify(resultJson)}`,
    );
  }

  const body = resultJson.isBase64Encoded
    ? Buffer.from(resultJson.body, "base64").toString()
    : resultJson.body;
  const bodyJson = body ? JSON.parse(body) : null;

  return bodyJson;
}
