import { environment } from "@/environments";
import {
  PutCommandOutput,
  PutCommand,
  QueryCommand,
  DeleteCommand,
  GetCommand,
} from "@aws-sdk/lib-dynamodb";
import { docClient } from "./client";
import { removeNullish } from "./util";

export interface ScaleSchedule {
  Type: "ScaleSchedule";
  ID: string;
  Data: {
    name: string;
    expectedMaxRps: number;
    programId: string;
    startAt: string; // HH:mm:ss
    endAt: string; // HH:mm:ss
    weekDays: string[];
  };
}

export async function fetchScaleSchedule(
  scaleScheduleId: string,
): Promise<ScaleSchedule | null> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "ScaleSchedule",
      ID: scaleScheduleId,
    },
  });

  const response = await docClient.send(command);
  return (response.Item as ScaleSchedule) ?? null;
}

export async function fetchScaleSchedules(): Promise<ScaleSchedule[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": "ScaleSchedule",
    },
  });

  const response = await docClient.send(command);
  return (response.Items as ScaleSchedule[]) ?? [];
}

export async function putScaleSchedule(
  id: string,
  name: string,
  programId: string,
  expectedMaxRps: number,
  startAt: string,
  endAt: string,
  weekDays: string[],
): Promise<PutCommandOutput> {
  const scheduledPoll: ScaleSchedule = {
    Type: "ScaleSchedule",
    ID: id,
    Data: {
      name,
      programId,
      expectedMaxRps,
      startAt,
      endAt,
      weekDays,
    },
  };

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: removeNullish(scheduledPoll),
  });

  const response = await docClient.send(command);
  return response;
}

export async function deleteScaleSchedule(id: string): Promise<void> {
  await docClient.send(
    new DeleteCommand({
      TableName: environment.cmsTableName,
      Key: {
        Type: "ScaleSchedule",
        ID: id,
      },
    }),
  );
}
