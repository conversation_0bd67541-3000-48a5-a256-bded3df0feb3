import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  fetchUser,
  fetchUsers,
  saveUserCustomClaims,
  updateUserType,
  updateUserMemo,
  deleteUser,
} from "./user";

beforeAll(() => {
  mockDynamoDB();
});

// InternalUserCustomClaims
const fakeCustomClaims = {
  nhkManNumber: "123456",
  nhkName: "Test User",
  nhkNameKana: "テスト ユーザー",
  nhkDepartmentCode: "001",
  nhkDepartmentName: "Test Department",
  nhkExtensionNumber: "1234",
  nhkDirectNumber: "5678",
  email: "<EMAIL>",
  nhkAccountId: "testuser123",
};

describe("user", () => {
  it("should be created, fetched, updated, and deleted", async () => {
    // Create a user
    await saveUserCustomClaims("test-user", fakeCustomClaims);

    // Fetch the created user
    let user = await fetchUser("test-user");
    assert.deepEqual(user, {
      Type: "User",
      ID: "test-user",
      CustomClaims: fakeCustomClaims,
    });

    // Update the user type
    await updateUserType("test-user", "member");

    // Fetch the updated user
    user = await fetchUser("test-user");
    assert.deepEqual(user, {
      Type: "User",
      ID: "test-user",
      CustomClaims: fakeCustomClaims,
      UserType: "member",
    });

    // Update the user memo
    await updateUserMemo("test-user", "This is a test user.");

    // Fetch the updated user with memo
    user = await fetchUser("test-user");
    assert.deepEqual(user, {
      Type: "User",
      ID: "test-user",
      CustomClaims: fakeCustomClaims,
      UserType: "member",
      Memo: "This is a test user.",
    });

    // Fetch all users
    const users = await fetchUsers();
    assert.equal(users.length, 1);
    assert.deepEqual(users[0], user);

    // Delete the user
    await deleteUser("test-user");

    // Ensure the user is deleted
    user = await fetchUser("test-user");
    assert.equal(user, undefined);
  });
});
