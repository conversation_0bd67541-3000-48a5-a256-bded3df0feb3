import { environment } from "@/environments";
import {
  PutCommand,
  DeleteCommand,
  Que<PERSON><PERSON><PERSON><PERSON>,
  GetCommand,
  QueryCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { client, docClient } from "./client";
import { deleteAll } from "./util";

export interface ProgramPermission {
  Type: `ProgramPermission#${string}`; // ProgramPermission#{userId}
  ID: string; // programId
}

export async function createProgramPermission(
  userId: string,
  programId: string,
) {
  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: {
      Type: `ProgramPermission#${userId}`,
      ID: programId,
    },
  });

  await client.send(command);
}

export async function deleteProgramPermission(programId: string) {
  const input: QueryCommandInput = {
    TableName: environment.cmsTableName,
    IndexName: environment.cmsTableIdTypeIndexName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "ID",
      "#sk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": programId,
      ":skValue": "ProgramPermission#",
    },
  };

  await deleteAll(environment.cmsTableName!, input);
}

export async function deleteProgramPermissionByUserId(
  userId: string,
  programId: string,
) {
  const command = new DeleteCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: `ProgramPermission#${userId}`,
      ID: programId,
    },
  });

  await client.send(command);
}

export async function updatePermissions(
  programId: string,
  newPermittedUsers: string[],
) {
  const currentPermissions =
    await fetchProgramPermissionsByProgramId(programId);
  const currentUsers = currentPermissions.map((p) => p.Type.split("#")[1]);

  const toAdd = newPermittedUsers.filter((u) => !currentUsers.includes(u));
  const toDelete = currentUsers.filter((u) => !newPermittedUsers.includes(u));

  await Promise.all([
    ...toAdd.map((userId) => createProgramPermission(userId, programId)),
    ...toDelete.map((userId) =>
      deleteProgramPermissionByUserId(userId, programId),
    ),
  ]);
}

export async function fetchProgramPermissionsByProgramId(
  programId: string,
): Promise<ProgramPermission[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    IndexName: environment.cmsTableIdTypeIndexName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "ID",
      "#sk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": programId,
      ":skValue": "ProgramPermission#",
    },
  });

  const response = await docClient.send(command);
  return (response.Items as ProgramPermission[]) ?? [];
}

export async function fetchProgramPermissionsByUserId(
  userId: string,
): Promise<ProgramPermission[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": `ProgramPermission#${userId}`,
    },
  });

  const response = await docClient.send(command);
  return (response.Items as ProgramPermission[]) ?? [];
}

export async function fetchProgramPermission(
  programId: string,
  userId: string,
): Promise<ProgramPermission | undefined> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: `ProgramPermission#${userId}`,
      ID: programId,
    },
  });

  const response = await docClient.send(command);
  return response.Item as ProgramPermission;
}
