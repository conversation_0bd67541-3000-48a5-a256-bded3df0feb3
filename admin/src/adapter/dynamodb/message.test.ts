import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  createMessageTopic,
  deleteMessageTopics,
  fetchMessageTopics,
} from "./message";
import { mockedGetNow, mockedTime } from "@/test/mock-time";

beforeAll(() => {
  mockDynamoDB();
});

vi.mock("@/utils/time", async () => {
  return {
    getNow: () => mockedGetNow(),
  };
});

describe("messages", () => {
  it("should be created and fetched", async () => {
    const result = await createMessageTopic(
      "test-program",
      "test-topic",
      "test-name",
    );
    assert.equal(result, "success");

    const messages = await fetchMessageTopics("test-program");
    assert.equal(messages.length, 1);
    assert.deepEqual(messages[0], {
      Type: "MessageTopic",
      ID: "test-program#test-topic",
      Data: { name: "test-name" },
      Timestamp: mockedTime,
    });
  });

  it("should not be created if ID already exists", async () => {
    const result = await createMessageTopic(
      "test-program",
      "test-topic",
      "test-name",
    );
    assert.equal(result, "id_already_exists");
  });

  it.concurrent("should be deleted", async () => {
    await createMessageTopic("test-program2", "test-topic", "test-name");
    await createMessageTopic("test-program2", "test-topic2", "test-name2");

    const messages = await fetchMessageTopics("test-program2");
    assert.equal(messages.length, 2);

    await deleteMessageTopics("test-program2");

    const messagesAfterDelete = await fetchMessageTopics("test-program2");
    assert.equal(messagesAfterDelete.length, 0);
  });
});
