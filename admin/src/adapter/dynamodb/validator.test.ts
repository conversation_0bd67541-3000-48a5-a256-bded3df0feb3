import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import { fetchValidator, saveValidator, deleteValidator } from "./validator";

beforeAll(() => {
  mockDynamoDB();
});

describe("validator", () => {
  it("should be created, fetched, and deleted", async () => {
    const programId = "test-program";
    const customValidator = {
      groupId: { gte: -1000, lte: 1000 },
      topicId: { gte: -1000, lte: 1000 },
      voteValue: { gte: 1, lte: 5 },
    };

    // Save a validator
    await saveValidator(programId, customValidator);

    // Fetch the saved validator
    let validator = await fetchValidator(programId);
    assert.deepEqual(validator, {
      Type: "Validator",
      ID: programId,
      Data: customValidator,
    });

    // Delete the validator
    await deleteValidator(programId);

    // Ensure the validator is deleted
    validator = await fetchValidator(programId);
    assert.equal(validator, null);
  });

  it("should return null if validator does not exist", async () => {
    const validator = await fetchValidator("non-existent-program");
    assert.equal(validator, null);
  });
});
