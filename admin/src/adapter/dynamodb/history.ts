import { environment } from "@/environments";
import { PutCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import "dayjs/locale/ja";
import { client, docClient } from "./client";
import { deleteAll, removeNullish } from "./util";

dayjs.extend(utc);

export interface HistoryItem {
  Type: "History";
  ID: string; // timestamp ex. 2024-09-13T07:02:44.741Z
  Data: ActionType;
  UserId: string;
}

export type ActionType =
  | {
      kind: "login";
    }
  | {
      kind: "logout";
    }
  | {
      kind: "program#create";
      programId: string;
      name: string;
    }
  | {
      kind: "program#update-name";
      programId: string;
      name: string;
    }
  | {
      kind: "program#update-expires-at";
      programId: string;
    }
  | {
      kind: "program#create-message-topic";
      programId: string;
      messageTopicId: string;
    }
  | {
      kind: "program#update-permission";
      programId: string;
    }
  | {
      kind: "program#update-validation";
      programId: string;
    }
  | {
      kind: "program#delete";
      programId: string;
    }
  | {
      kind: "program#delete-message-topic";
      programId: string;
      messageTopicId: string;
    }
  // program-scale: 番組表API連携
  | {
      kind: "program-scale#update";
      programId: string;
      seriesId?: string;
    }
  | {
      kind: "scale#create";
      scaleId: string;
    }
  | {
      kind: "scale#update";
      scaleId: string;
    }
  | {
      kind: "scale#delete";
      scaleId: string;
    }
  | {
      kind: "scale-schedule#create";
      scaleScheduleId: string;
    }
  | {
      kind: "scale-schedule#update";
      scaleScheduleId: string;
    }
  | {
      kind: "scale-schedule#delete";
      scaleScheduleId: string;
    }
  | {
      kind: "aggApiToken#create";
      programId?: string;
      name: string;
    }
  | {
      kind: "aggApiToken#update-expires-at";
      programId?: string;
      name: string;
    }
  | {
      kind: "aggApiToken#delete";
      tokenId: string;
      programId?: string;
      name: string;
    }
  | {
      kind: "admin#switch-user-role";
      userId: string;
      to: "admin" | "member";
    }
  | {
      kind: "admin#update-user-note";
      userId: string;
    }
  | {
      kind: "admin#delete-user";
      userId: string;
    };

export async function addLoginHistory(userId: string) {
  await addHistory(userId, { kind: "login" });
}

export async function addHistory(userId: string, action: ActionType) {
  const now = dayjs().utc().toISOString();

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: {
      Type: "History",
      ID: now,
      Data: removeNullish(action),
      UserId: userId,
    },
  });

  await client.send(command);
}

export async function fetchUserHistory(
  userId: string,
  lastKey?: string,
  date?: string, // YYYY-MM-DD
): Promise<{
  items: HistoryItem[];
  lastKey?: string;
}> {
  let command: QueryCommand;
  if (date != null) {
    // dateは日本時間の日付で、DynamoDBにはUTCで保存しているので日本時間での一日の区間をクエリする
    const start = dayjs(date).locale("ja").startOf("day").utc().toISOString();
    const end = dayjs(date).locale("ja").endOf("day").utc().toISOString();

    command = new QueryCommand({
      TableName: environment.cmsTableName,
      IndexName: environment.cmsTableUserIdIdIndexName,
      KeyConditionExpression: "#pk = :pkValue AND #sk BETWEEN :start AND :end",
      ExpressionAttributeNames: {
        "#pk": "UserId",
        "#sk": "ID",
      },
      ExpressionAttributeValues: {
        ":pkValue": userId,
        ":start": start,
        ":end": end,
      },
      Limit: 1000,
      ScanIndexForward: false,
    });
  } else {
    command = new QueryCommand({
      TableName: environment.cmsTableName,
      IndexName: environment.cmsTableUserIdIdIndexName,
      KeyConditionExpression: "#pk = :pkValue",
      ExpressionAttributeNames: {
        "#pk": "UserId",
      },
      ExpressionAttributeValues: {
        ":pkValue": userId,
      },
      Limit: 1000,
      ScanIndexForward: false,
    });
  }

  if (lastKey != null) {
    command.input.ExclusiveStartKey = {
      Type: "History",
      ID: lastKey,
      UserId: userId,
    };
  }

  const response = await docClient.send(command);
  const items = (response.Items?.filter((item) => item.Type === "History") ??
    []) as HistoryItem[];
  return { items, lastKey: response.LastEvaluatedKey?.ID };
}

export async function fetchHistory(
  lastKey?: string,
  date?: string, // YYYY-MM-DD
): Promise<{
  items: HistoryItem[];
  lastKey?: string;
}> {
  let command: QueryCommand;
  if (date != null) {
    // dateは日本時間の日付で、DynamoDBにはUTCで保存しているので日本時間での一日の区間をクエリする
    const start = dayjs(date).locale("ja").startOf("day").utc().toISOString();
    const end = dayjs(date).locale("ja").endOf("day").utc().toISOString();
    command = new QueryCommand({
      TableName: environment.cmsTableName,
      KeyConditionExpression: "#pk = :pkValue AND #sk BETWEEN :start AND :end",
      ExpressionAttributeNames: {
        "#pk": "Type",
        "#sk": "ID",
      },
      ExpressionAttributeValues: {
        ":pkValue": "History",
        ":start": start,
        ":end": end,
      },
      Limit: 1000,
      ScanIndexForward: false,
    });
  } else {
    command = new QueryCommand({
      TableName: environment.cmsTableName,
      KeyConditionExpression: "#pk = :pkValue",
      ExpressionAttributeNames: {
        "#pk": "Type",
      },
      ExpressionAttributeValues: {
        ":pkValue": "History",
      },
      Limit: 1000,
      ScanIndexForward: false,
    });
  }

  if (lastKey != null) {
    command.input.ExclusiveStartKey = {
      Type: "History",
      ID: lastKey,
    };
  }

  const response = await docClient.send(command);
  const items = (response.Items as HistoryItem[]) ?? [];

  return { items, lastKey: response.LastEvaluatedKey?.ID };
}

export async function deleteUserHistory(userId: string) {
  const input = {
    TableName: environment.cmsTableName,
    IndexName: environment.cmsTableUserIdIdIndexName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "UserId",
    },
    ExpressionAttributeValues: {
      ":pkValue": userId,
    },
    Limit: 1000,
  };

  await deleteAll(environment.cmsTableName!, input);
}
