import { environment } from "@/environments";
import { QueryCommand } from "@aws-sdk/lib-dynamodb";
import { docClient } from "./client";
import { deleteAll } from "./util";

export interface VoteTopic {
  Type: "VoteTopic";
  ID: string;
  Timestamp: string;
}

export async function fetchVoteTopics(programId: string): Promise<VoteTopic[]> {
  const command = new QueryCommand({
    TableName: environment.voteTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "VoteTopic",
      ":skValue": `${programId}#`,
    },
  });

  const response = await docClient.send(command);
  return (response.Items as VoteTopic[]) ?? [];
}

export async function deleteVotesByGroup(programId: string, groupId: string) {
  const topics = await fetchVoteTopicsByGroup(programId, groupId);

  for (const topic of topics) {
    // Voteの削除
    await deleteAll(environment.voteTableName!, {
      TableName: environment.voteTableName,
      IndexName: environment.voteTableIdTypeIndexName,
      KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
      ExpressionAttributeNames: {
        "#pk": "ID",
        "#sk": "Type",
      },
      ExpressionAttributeValues: {
        ":pkValue": topic.ID,
        ":skValue": "Vote#",
      },
    });

    // SecondAggregatedVoteの削除
    await deleteAll(environment.voteTableName!, {
      TableName: environment.voteTableName,
      IndexName: environment.voteTableIdTypeIndexName,
      KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
      ExpressionAttributeNames: {
        "#pk": "ID",
        "#sk": "Type",
      },
      ExpressionAttributeValues: {
        ":pkValue": topic.ID,
        ":skValue": "SecondAggregatedVote#",
      },
    });
  }

  // AggregatedVoteの削除
  await deleteAll(environment.voteTableName!, {
    TableName: environment.voteTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "AggregatedVote",
      ":skValue": `${programId}#${groupId}#`,
    },
  });

  // VoteTopicの削除
  await deleteAll(environment.voteTableName!, {
    TableName: environment.voteTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "VoteTopic",
      ":skValue": `${programId}#${groupId}#`,
    },
  });
}

async function fetchVoteTopicsByGroup(
  programId: string,
  groupId: string,
): Promise<VoteTopic[]> {
  const command = new QueryCommand({
    TableName: environment.voteTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "VoteTopic",
      ":skValue": `${programId}#${groupId}#`,
    },
  });

  const response = await docClient.send(command);
  return (response.Items as VoteTopic[]) ?? [];
}
