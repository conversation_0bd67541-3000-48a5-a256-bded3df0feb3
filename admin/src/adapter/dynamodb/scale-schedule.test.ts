import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  fetchScaleSchedules,
  putScaleSchedule,
  deleteScaleSchedule,
} from "./scale-schedule";

beforeAll(() => {
  mockDynamoDB();
});

describe("scale schedule", () => {
  it("should be created, fetched, and deleted", async () => {
    // Create a scale schedule
    const createResult = await putScaleSchedule(
      "test-schedule",
      "Test Schedule",
      "test-program",
      100,
      "08:00:00",
      "18:00:00",
      ["mon", "tue"],
    );
    assert.equal(createResult.$metadata.httpStatusCode, 200);

    // Fetch the created scale schedule
    let schedules = await fetchScaleSchedules();
    assert.equal(schedules.length, 1);
    assert.deepEqual(schedules[0], {
      Type: "ScaleSchedule",
      ID: "test-schedule",
      Data: {
        name: "Test Schedule",
        programId: "test-program",
        expectedMaxRps: 100,
        startAt: "08:00:00",
        endAt: "18:00:00",
        weekDays: ["mon", "tue"],
      },
    });

    // Delete the scale schedule
    await deleteScaleSchedule("test-schedule");

    // Ensure the scale schedule is deleted
    schedules = await fetchScaleSchedules();
    assert.equal(schedules.length, 0);
  });
});
