import { environment } from "@/environments";
import {
  DeleteCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
} from "@aws-sdk/lib-dynamodb";
import { client, docClient } from "./client";
import { CustomClaims } from "@/auth";

export interface User {
  Type: "User";

  ID: string;
  CustomClaims?: CustomClaims;
  UserType?: "admin" | "member";
  Memo?: string;
}

export async function saveUserCustomClaims(
  userId: string,
  claims: CustomClaims,
) {
  const command = new UpdateCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "User",
      ID: userId,
    },
    UpdateExpression: "set CustomClaims = :customClaims",
    ExpressionAttributeValues: {
      ":customClaims": claims,
    },
  });

  await client.send(command);
}

export async function fetchUsers(): Promise<User[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": "User",
    },
  });

  const response = await docClient.send(command);
  return (response.Items as User[]) ?? [];
}

export async function searchUsersPaging(
  search?: string,
  lastKey?: string,
): Promise<{
  users: User[];
  lastKey?: string;
}> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": "User",
    },
  });

  if (lastKey != null) {
    command.input.ExclusiveStartKey = {
      Type: "User",
      ID: lastKey,
    };
  }

  const response = await docClient.send(command);
  let users = (response.Items as User[]) ?? [];

  if (search) {
    users = users.filter((u) => {
      if (u.ID.includes(search) || u.Memo?.includes(search)) {
        return true;
      }
      const values = Object.values(u.CustomClaims ?? {});
      return values.some((v) => v.includes(search));
    });
  }

  return { users, lastKey: response.LastEvaluatedKey?.ID };
}

export async function fetchUser(userId: string): Promise<User | undefined> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "User",
      ID: userId,
    },
  });

  const response = await docClient.send(command);
  return response.Item as User;
}

export async function updateUserType(
  userId: string,
  userType: "admin" | "member",
) {
  const command = new UpdateCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "User",
      ID: userId,
    },
    UpdateExpression: "set #key = :data",
    ExpressionAttributeNames: {
      "#key": "UserType",
    },
    ExpressionAttributeValues: {
      ":data": userType,
    },
  });

  await client.send(command);
}

export async function updateUserMemo(userId: string, memo: string) {
  const command = new UpdateCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "User",
      ID: userId,
    },
    UpdateExpression: "set Memo = :memo",
    ExpressionAttributeValues: {
      ":memo": memo,
    },
  });

  await client.send(command);
}

export async function deleteUser(userId: string) {
  const command = new DeleteCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "User",
      ID: userId,
    },
  });

  await client.send(command);
}
