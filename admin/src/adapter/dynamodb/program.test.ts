import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  createProgram,
  fetchProgram,
  fetchPrograms,
  updateProgram,
  deleteProgram,
} from "./program";

beforeAll(() => {
  mockDynamoDB();
});

describe("program", () => {
  it("should be created, fetched, updated, and deleted", async () => {
    // Create a program
    const createResult = await createProgram(
      "test-program",
      "Test Program",
      "2024-01-01",
    );
    assert.equal(createResult, "success");

    // Fetch the created program
    let program = await fetchProgram("test-program");
    assert.deepEqual(program, {
      Type: "Program",
      ID: "test-program",
      Data: { name: "Test Program", expiresAt: "2024-01-01" },
    });

    // Update the program
    await updateProgram("test-program", "Updated Test Program", "2024-01-02", {
      seriesId: "series-1",
      expectedMaxRps: 100,
      marginHour: 2,
    });

    // Fetch the updated program
    program = await fetchProgram("test-program");
    assert.deepEqual(program, {
      Type: "Program",
      ID: "test-program",
      Data: {
        name: "Updated Test Program",
        expiresAt: "2024-01-02",
        scale: {
          seriesId: "series-1",
          expectedMaxRps: 100,
          marginHour: 2,
        },
      },
    });

    // Fetch all programs
    const programs = await fetchPrograms();
    assert.equal(programs.length, 1);
    assert.deepEqual(programs[0], program);

    // Delete the program
    await deleteProgram("test-program");

    // Ensure the program is deleted
    program = await fetchProgram("test-program");
    assert.equal(program, null);
  });
});
