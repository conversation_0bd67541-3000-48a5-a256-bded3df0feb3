import { environment } from "@/environments";
import {
  DeleteCommand,
  GetCommand,
  PutCommand,
  QueryCommand,
} from "@aws-sdk/lib-dynamodb";
import { docClient } from "./client";
import { v7 } from "uuid";
import { randomBytes } from "crypto";
import { deleteAll, removeNullish } from "./util";

export interface AggAPIAccessTokenRecord {
  Type: "AggAPIAccessToken";
  ID: string; // "program_id#token_id" もしくは対象番組を持たない場合 "token_id"
  Data: {
    name: string;
    token: string; // トークン文字列
    expiresAt?: string; // 対象番組を持たない場合のみ設定される YYYY-MM-DD
  };
}

export async function fetchAggAPIAccessToken(
  id: string,
): Promise<AggAPIAccessTokenRecord | null> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "AggAPIAccessToken",
      ID: id,
    },
  });

  const response = await docClient.send(command);
  return (response.Item as AggAPIAccessTokenRecord) ?? null;
}

export async function fetchAggAPIAccessTokens(): Promise<
  AggAPIAccessTokenRecord[]
> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": "AggAPIAccessToken",
    },
  });

  const response = await docClient.send(command);
  return (response.Items as AggAPIAccessTokenRecord[]) ?? [];
}

export async function fetchAggAPIAccessTokensByProgram(
  programId: string,
): Promise<AggAPIAccessTokenRecord[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "AggAPIAccessToken",
      ":skValue": `${programId}#`,
    },
  });

  const response = await docClient.send(command);
  return (response.Items as AggAPIAccessTokenRecord[]) ?? [];
}

export async function createAggAPIAccessToken(
  name: string,
  programId: string | undefined,
  expiresAt?: string,
): Promise<string> {
  const id = programId ? `${programId}#${v7()}` : v7();
  const token = randomBytes(32).toString("base64url");

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: {
      Type: "AggAPIAccessToken",
      ID: id,
      Data: removeNullish({
        name,
        token,
        expiresAt,
      }),
    },
  });

  await docClient.send(command);
  return id;
}

export async function saveAggAPIAccessToken(
  id: string,
  name: string,
  expiresAt?: string,
): Promise<void> {
  const token = randomBytes(32).toString("base64url");

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: {
      Type: "AggAPIAccessToken",
      ID: id,
      Data: removeNullish({
        name,
        token,
        expiresAt,
      }),
    },
  });

  await docClient.send(command);
}

export async function deleteAggAPIAccessToken(id: string): Promise<void> {
  await docClient.send(
    new DeleteCommand({
      TableName: environment.cmsTableName,
      Key: {
        Type: "AggAPIAccessToken",
        ID: id,
      },
    }),
  );
}

export async function deleteAggAPIAccessTokenByProgram(
  programId: string,
): Promise<void> {
  await deleteAll(environment.cmsTableName!, {
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "AggAPIAccessToken",
      ":skValue": `${programId}#`,
    },
  });
}
