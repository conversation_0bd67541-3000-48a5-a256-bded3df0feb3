import { QueryCommand, QueryCommandInput } from "@aws-sdk/lib-dynamodb";
import { BatchWriteItemCommand } from "@aws-sdk/client-dynamodb";
import { marshall } from "@aws-sdk/util-dynamodb";
import { docClient, client } from "./client";

// クエリ条件にマッチする全てのアイテムを削除する
export async function deleteAll(
  tableName: string,
  queryCommandInput: QueryCommandInput,
) {
  queryCommandInput.Limit = 25;

  while (true) {
    const command = new QueryCommand(queryCommandInput);
    const res = await docClient.send(command);
    const items = res.Items ?? [];

    if (items.length > 0) {
      const batchDeleteCommand = new BatchWriteItemCommand({
        RequestItems: {
          [tableName]: items.map((item) => ({
            DeleteRequest: {
              Key: marshall({
                ID: item.ID,
                Type: item.Type,
              }),
            },
          })),
        },
      });

      await client.send(batchDeleteCommand);
    }

    if (!res.LastEvaluatedKey) {
      break;
    }

    queryCommandInput.ExclusiveStartKey = res.LastEvaluatedKey;
  }
}

// DynamoDBのマップに foo: undefined のような値を書くとエラーになるので undefined や null を削除する
export function removeNullish<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj, (k, v) => (v == null ? undefined : v)));
}
