import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import { mockedGetNow } from "@/test/mock-time";
import {
  createProgramPermission,
  fetchProgramPermission,
  fetchProgramPermissionsByProgramId,
  fetchProgramPermissionsByUserId,
} from "./program-permission";

beforeAll(() => {
  mockDynamoDB();
});

vi.mock("@/utils/time", async () => {
  return {
    getNow: () => mockedGetNow(),
  };
});

describe("program permission", () => {
  it("should be created and fetched", async () => {
    await createProgramPermission("test-user", "test-program");

    let permissions = await fetchProgramPermissionsByProgramId("test-program");
    assert.equal(permissions.length, 1);
    assert.deepEqual(permissions[0], {
      Type: "ProgramPermission#test-user",
      ID: "test-program",
    });

    permissions = await fetchProgramPermissionsByUserId("test-user");
    assert.equal(permissions.length, 1);
    assert.deepEqual(permissions[0], {
      Type: "ProgramPermission#test-user",
      ID: "test-program",
    });

    const permission = await fetchProgramPermission(
      "test-program",
      "test-user",
    );
    assert.deepEqual(permission, {
      Type: "ProgramPermission#test-user",
      ID: "test-program",
    });
  });
});
