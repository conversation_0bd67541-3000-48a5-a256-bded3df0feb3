import { environment } from "@/environments";
import { DeleteCommand, GetCommand, PutCommand } from "@aws-sdk/lib-dynamodb";
import { docClient } from "./client";

export interface Condition {
  gte: number;
  lte: number;
}

export interface Validator {
  groupId: Condition;
  topicId: Condition;
  voteValue: Condition;
}

export const defaultValidator = {
  groupId: { gte: -9999, lte: 9999 },
  topicId: { gte: -999, lte: 999 },
  voteValue: { gte: 0, lte: 999999 },
};

export interface ValidatorRecord {
  Type: "Validator";
  ID: string; // programId
  Data: Validator;
}

export async function fetchValidator(
  programId: string,
): Promise<ValidatorRecord | null> {
  const command = new GetCommand({
    TableName: environment.voteTableName,
    Key: {
      Type: "Validator",
      ID: programId,
    },
  });

  const response = await docClient.send(command);
  return (response.Item as ValidatorRecord) ?? null;
}

export async function saveValidator(
  programId: string,
  data: Validator,
): Promise<void> {
  const command = new PutCommand({
    TableName: environment.voteTableName,
    Item: {
      Type: "Validator",
      ID: programId,
      Data: data,
    },
  });

  await docClient.send(command);
}

export async function deleteValidator(programId: string): Promise<void> {
  const command = new DeleteCommand({
    TableName: environment.voteTableName,
    Key: {
      Type: "Validator",
      ID: programId,
    },
  });

  await docClient.send(command);
}
