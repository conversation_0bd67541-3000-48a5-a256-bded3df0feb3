import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  fetchScale,
  putScale,
  deleteScale,
  fetchScalesAfter,
  fetchScalesBefore,
} from "./scale";
import dayjs from "dayjs";

beforeAll(() => {
  mockDynamoDB();
});

describe("scale", () => {
  it("should be created, fetched, and deleted", async () => {
    const startTime = dayjs().utc().format();
    const endTime = dayjs().add(1, "hour").utc().format();

    // Create a scale
    const createResult = await putScale(
      "test-scale",
      "Test Scale",
      "test-program",
      startTime,
      endTime,
      100,
    );
    assert.equal(createResult.$metadata.httpStatusCode, 200);

    // Fetch the created scale
    let scale = await fetchScale("test-scale");
    assert.deepEqual(scale, {
      Type: "Scale",
      ID: "test-scale",
      Data: {
        name: "Test Scale",
        programId: "test-program",
        expectedMaxRps: 100,
      },
      StartTime: startTime,
      EndTime: endTime,
    });

    // Delete the scale
    await deleteScale("test-scale");

    // Ensure the scale is deleted
    scale = await fetchScale("test-scale");
    assert.equal(scale, undefined);
  });

  it("should fetch scales after a specific time", async () => {
    const target = dayjs().utc();
    const startTime1 = target.subtract(3, "hours").format();
    const endTime1 = target.subtract(2, "hours").format();
    const startTime2 = target.subtract(2, "hours").format();
    const endTime2 = target.subtract(1, "hour").format();
    const startTime3 = target.format();
    const endTime3 = target.add(1, "hour").format();

    await putScale(
      "scale-1",
      "Scale 1",
      "test-program",
      startTime1,
      endTime1,
      75,
    );
    await putScale(
      "scale-2",
      "Scale 2",
      "test-program",
      startTime2,
      endTime2,
      50,
    );
    await putScale(
      "scale-3",
      "Scale 3",
      "test-program",
      startTime3,
      endTime3,
      100,
    );

    const scales = await fetchScalesAfter(target.subtract(1, "hour"));
    assert.equal(scales.length, 2);
  });

  it("should fetch scales before a specific time", async () => {
    const target = dayjs().subtract(1, "day").utc();
    const startTime1 = target.subtract(2, "hours").format();
    const endTime1 = target.subtract(1, "hour").format();
    const startTime2 = target.subtract(1, "hour").format();
    const endTime2 = target.format();

    await putScale(
      "scale-11",
      "Scale 11",
      "test-program",
      startTime1,
      endTime1,
      50,
    );
    await putScale(
      "scale-12",
      "Scale 12",
      "test-program",
      startTime2,
      endTime2,
      100,
    );

    const scales = await fetchScalesBefore(target.subtract(30, "minutes"));
    assert.equal(scales.length, 1);
  });
});
