import {
  DeleteItemCommand,
  DeleteItemCommandOutput,
} from "@aws-sdk/client-dynamodb";
import {
  GetCommand,
  PutCommand,
  PutCommandOutput,
  QueryCommand,
} from "@aws-sdk/lib-dynamodb";
import { environment } from "@/environments";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { docClient } from "./client";

dayjs.extend(utc);

export interface Scale {
  Type: string; // Scale
  ID: string;
  Data: {
    name: string;
    expectedMaxRps: number;
    programId: string;
    source?:
      | {
          kind: "schedule";
          programId: string;
          scaleScheduleId: string;
        }
      | {
          kind: "program";
          programId: string;
          broadcastEventId: string;
          broadcastEventName: string;
        };
  };
  StartTime: string;
  EndTime: string;
}

export async function fetchScale(scaleId: string): Promise<Scale | undefined> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "Scale",
      ID: scaleId,
    },
  });

  const response = await docClient.send(command);
  return response.Item as Scale | undefined;
}

export async function putScale(
  scaleId: string,
  name: string,
  programId: string,
  startTime: string,
  endTime: string,
  expectedMaxRps: number,
): Promise<PutCommandOutput> {
  const saved = await fetchScale(scaleId);
  const scale: Scale = {
    Type: "Scale",
    ID: scaleId,
    Data: {
      ...saved?.Data,
      name,
      programId,
      expectedMaxRps,
    },
    StartTime: dayjs(startTime).utc().format(),
    EndTime: dayjs(endTime).utc().format(),
  };

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: scale,
  });

  const response = await docClient.send(command);
  return response;
}

export async function deleteScale(
  scaleId: string,
): Promise<DeleteItemCommandOutput> {
  const command = new DeleteItemCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: {
        S: "Scale",
      },
      ID: {
        S: scaleId,
      },
    },
  });

  const output = await docClient.send(command);
  return output;
}

export async function fetchScalesAfter(time: dayjs.Dayjs): Promise<Scale[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    IndexName: environment.cmsTableTypeEndTimeIndexName,
    KeyConditionExpression: "#pk = :pkValue AND #sk >= :endTime",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "EndTime",
    },
    ExpressionAttributeValues: {
      ":pkValue": "Scale",
      ":endTime": time.utc().format("YYYY-MM-DDTHH:mm:ssZ"),
    },
  });

  const response = await docClient.send(command);
  return (response.Items as Scale[]) ?? [];
}

export async function fetchScalesBefore(time: dayjs.Dayjs): Promise<Scale[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    IndexName: environment.cmsTableTypeEndTimeIndexName,
    KeyConditionExpression: "#pk = :pkValue AND #sk < :endTime",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "EndTime",
    },
    ExpressionAttributeValues: {
      ":pkValue": "Scale",
      ":endTime": time.utc().format("YYYY-MM-DDTHH:mm:ssZ"),
    },
  });

  const response = await docClient.send(command);
  return (response.Items as Scale[]) ?? [];
}
