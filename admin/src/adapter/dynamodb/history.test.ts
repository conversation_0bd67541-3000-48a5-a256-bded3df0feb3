import { assert, beforeAll, describe, it } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  addHistory,
  addLoginHistory,
  fetchHistory,
  HistoryItem,
} from "./history";

beforeAll(() => {
  mockDynamoDB();
});

describe("history", () => {
  it.concurrent("should be added and fetched", async () => {
    await addLoginHistory("test-user");
    await addHistory("test-user", {
      kind: "program#create",
      programId: "test-program",
      name: "test-name",
    });
    await addHistory("test-user", {
      kind: "program#update-name",
      programId: "test-program",
      name: "test-name",
    });

    const { items } = await fetchHistory();
    assert.equal(items.length, 3);
    assertHistory(items[0], {
      UserId: "test-user",
      Data: {
        kind: "program#update-name",
        programId: "test-program",
        name: "test-name",
      },
    });
    assertHistory(items[1], {
      UserId: "test-user",
      Data: {
        kind: "program#create",
        programId: "test-program",
        name: "test-name",
      },
    });
    assertHistory(items[2], {
      UserId: "test-user",
      Data: { kind: "login" },
    });
  });
});

// HistoryItemのIDを除き、TypeはHistoryに固定してexpectedと比較する
function assertHistory(
  item: HistoryItem,
  expected: Omit<HistoryItem, "ID" | "Type">,
) {
  const { ID, ..._item } = item;
  assert.deepEqual(_item, { ...expected, Type: "History" });
}
