import "server-only";
import { Dynamo<PERSON><PERSON>lient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { fromEnv, fromSSO } from "@aws-sdk/credential-providers";
import { environment } from "@/environments";

export const client = getDynamoDBClient();
export const docClient = DynamoDBDocumentClient.from(client);

function getDynamoDBClient() {
  if (environment.local) {
    // return new DynamoDBClient({ endpoint: "http://localhost:8000" });
    return new DynamoDBClient({
      region: "ap-northeast-1",
      credentials: fromSSO(),
    });
  }

  return new DynamoDBClient({
    region: "ap-northeast-1",
    credentials: fromEnv(),
  });
}
