import { environment } from "@/environments";
import {
  DeleteCommand,
  PutCommand,
  Query<PERSON>ommand,
  QueryCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { client, docClient } from "./client";
import { ConditionalCheckFailedException } from "@aws-sdk/client-dynamodb";
import { deleteAll } from "./util";
import { getNow } from "@/utils/time";

export interface MessageTopic {
  Type: "MessageTopic";
  ID: string; // programId#topicId
  Data: {
    name: string;
  };
  Timestamp: string;
}

export async function fetchMessageTopics(
  programId: string,
): Promise<MessageTopic[]> {
  const command = new QueryCommand(messageTopicQueryInput(programId));

  const response = await docClient.send(command);
  return (response.Items as MessageTopic[]) ?? [];
}

export async function createMessageTopic(
  programId: string,
  topicId: string,
  name: string,
): Promise<"success" | "id_already_exists"> {
  const now = getNow().utc().toISOString();
  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: {
      Type: "MessageTopic",
      ID: `${programId}#${topicId}`,
      Data: { name },
      Timestamp: now,
    },
    ConditionExpression: "attribute_not_exists(ID)",
  });

  try {
    const output = await client.send(command);
    if (output.$metadata.httpStatusCode !== 200) {
      throw new Error("メッセージトピックの作成に失敗しました。", {
        cause: output,
      });
    }

    return "success";
  } catch (error) {
    if (error instanceof ConditionalCheckFailedException) {
      return "id_already_exists";
    }

    throw error;
  }
}

export async function deleteMessageTopic(programId: string, topicId: string) {
  const command = new DeleteCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "MessageTopic",
      ID: `${programId}#${topicId}`,
    },
  });

  await client.send(command);
}

export async function deleteMessageTopics(programId: string) {
  const input = messageTopicQueryInput(programId);
  await deleteAll(environment.cmsTableName!, input);
}

function messageTopicQueryInput(programId: string): QueryCommandInput {
  return {
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue AND begins_with(#sk, :skValue)",
    ExpressionAttributeNames: {
      "#pk": "Type",
      "#sk": "ID",
    },
    ExpressionAttributeValues: {
      ":pkValue": "MessageTopic",
      ":skValue": `${programId}#`,
    },
  };
}
