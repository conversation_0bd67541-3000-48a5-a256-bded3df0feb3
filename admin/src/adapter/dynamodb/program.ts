import { environment } from "@/environments";
import { GetCommand, PutCommand, QueryCommand } from "@aws-sdk/lib-dynamodb";
import {
  ConditionalCheckFailedException,
  DeleteItemCommand,
} from "@aws-sdk/client-dynamodb";
import { docClient } from "./client";
import { removeNullish } from "./util";

export interface Program {
  Type: "Program";
  ID: string;
  Data: {
    name: string;
    expiresAt?: string;
    scale?: {
      seriesId: string;
      expectedMaxRps: number;
      marginHour: number;
    };
  };
}

export async function fetchPrograms(): Promise<Program[]> {
  const command = new QueryCommand({
    TableName: environment.cmsTableName,
    KeyConditionExpression: "#pk = :pkValue",
    ExpressionAttributeNames: {
      "#pk": "Type",
    },
    ExpressionAttributeValues: {
      ":pkValue": "Program",
    },
  });

  const response = await docClient.send(command);
  return (response.Items as Program[]) ?? [];
}

export async function fetchProgram(programId: string): Promise<Program | null> {
  const command = new GetCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: "Program",
      ID: programId,
    },
  });

  const response = await docClient.send(command);
  return (response.Item ?? null) as Program | null;
}

export async function createProgram(
  id: string,
  name: string,
  expiresAt: string,
): Promise<"success" | "id_already_exists"> {
  const program: Program = {
    Type: "Program",
    ID: id,
    Data: { expiresAt, name },
  };

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: program,
    ConditionExpression: "attribute_not_exists(ID)",
  });

  try {
    const output = await docClient.send(command);
    if (output.$metadata.httpStatusCode !== 200) {
      throw new Error("番組の作成に失敗しました。", { cause: output });
    }

    return "success";
  } catch (error) {
    if (error instanceof ConditionalCheckFailedException) {
      return "id_already_exists";
    }

    throw error;
  }
}

export async function updateProgram(
  id: string,
  name: string,
  expiresAt?: string,
  scale?: {
    seriesId: string;
    expectedMaxRps: number;
    marginHour: number;
  },
) {
  const program: Program = {
    Type: "Program",
    ID: id,
    Data: { name, expiresAt, scale },
  };

  const command = new PutCommand({
    TableName: environment.cmsTableName,
    Item: removeNullish(program),
    ConditionExpression: "attribute_exists(ID)",
  });

  const output = await docClient.send(command);
  if (output.$metadata.httpStatusCode !== 200) {
    throw new Error("番組の更新に失敗しました。", { cause: output });
  }
}

export async function deleteProgram(programId: string) {
  const command = new DeleteItemCommand({
    TableName: environment.cmsTableName,
    Key: {
      Type: {
        S: "Program",
      },
      ID: {
        S: programId,
      },
    },
  });

  const output = await docClient.send(command);
  if (output.$metadata.httpStatusCode !== 200) {
    throw new Error("番組の削除に失敗しました。", { cause: output });
  }

  return output;
}
