import { assert, beforeAll, describe, it, vi } from "vitest";
import { mockDynamoDB } from "@/test/util";
import {
  createAggAPIAccessToken,
  fetchAggAPIAccessToken,
  fetchAggAPIAccessTokens,
  fetchAggAPIAccessTokensByProgram,
  saveAgg<PERSON>IAccessToken,
  deleteAggAPIAccessToken,
  deleteAggAPIAccessTokenByProgram,
} from "./agg-api-access-token";

beforeAll(() => {
  mockDynamoDB();
  vi.mock("crypto", (original) => {
    return {
      ...original,
      randomBytes: () => "test-token",
    };
  });
});

describe("agg-api-access-token", () => {
  it("プログラムIDなしでトークンを作成、取得、削除する", async () => {
    const id = await createAggAPIAccessToken("Test Token", undefined);

    const token = await fetchAggAPIAccessToken(id);
    assert.deepEqual(token, {
      Type: "AggAPIAccessToken",
      ID: id,
      Data: {
        name: "Test Token",
        token: "test-token",
      },
    });

    await deleteAgg<PERSON>IAccessToken(id);

    const deletedToken = await fetchAggAPIAccessToken(id);
    assert.equal(deletedToken, null);
  });

  it("プログラムIDありでトークンを作成、取得、削除する", async () => {
    const id = await createAggAPIAccessToken("Program Token", "test-program");
    const token = await fetchAggAPIAccessToken(id);
    assert.deepEqual(token, {
      Type: "AggAPIAccessToken",
      ID: id,
      Data: {
        name: "Program Token",
        token: "test-token",
      },
    });

    await deleteAggAPIAccessToken(id);

    const deletedToken = await fetchAggAPIAccessToken(id);
    assert.equal(deletedToken, null);
  });

  it("期限付きでトークンを作成して取得する", async () => {
    const expiresAt = "2024-12-31";
    const id = await createAggAPIAccessToken(
      "Expiring Token",
      undefined,
      expiresAt,
    );

    const token = await fetchAggAPIAccessToken(id);
    assert.deepEqual(token, {
      Type: "AggAPIAccessToken",
      ID: id,
      Data: {
        name: "Expiring Token",
        token: "test-token",
        expiresAt,
      },
    });

    await deleteAggAPIAccessToken(id);
  });

  it("すべてのトークンを取得する", async () => {
    await createAggAPIAccessToken("Token 1", undefined);
    await createAggAPIAccessToken("Token 2", "program-1");
    await createAggAPIAccessToken("Token 3", "program-2");

    const tokens = await fetchAggAPIAccessTokens();
    assert.equal(tokens.length, 3);
  });

  it("プログラムごとにトークンを取得する", async () => {
    await createAggAPIAccessToken("Program Token 1", "test-program-2");
    await createAggAPIAccessToken("Program Token 2", "test-program-2");
    await createAggAPIAccessToken("Other Token", "other-program");

    const tokens = await fetchAggAPIAccessTokensByProgram("test-program-2");
    assert.equal(tokens.length, 2);
    tokens.forEach((token) => {
      assert.isTrue(token.ID.startsWith("test-program-2#"));
    });
  });

  it("トークンを保存および更新する", async () => {
    const tokenId = "test-save-token";
    await saveAggAPIAccessToken(tokenId, "Initial Name");

    let token = await fetchAggAPIAccessToken(tokenId);
    assert.equal(token?.Data.name, "Initial Name");

    await saveAggAPIAccessToken(tokenId, "Updated Name");

    token = await fetchAggAPIAccessToken(tokenId);
    assert.equal(token?.Data.name, "Updated Name");
  });

  it("プログラムのすべてのトークンを削除する", async () => {
    const programId = "test-delete-program";

    await createAggAPIAccessToken("Delete Token 1", programId);
    await createAggAPIAccessToken("Delete Token 2", programId);
    await createAggAPIAccessToken("Other Token", "test-not-delete-program");

    let tokens = await fetchAggAPIAccessTokensByProgram(programId);
    assert.equal(tokens.length, 2);

    await deleteAggAPIAccessTokenByProgram(programId);

    tokens = await fetchAggAPIAccessTokensByProgram(programId);
    assert.equal(tokens.length, 0);

    const otherTokens = await fetchAggAPIAccessTokensByProgram(
      "test-not-delete-program",
    );
    assert.equal(otherTokens.length, 1);
  });
});
