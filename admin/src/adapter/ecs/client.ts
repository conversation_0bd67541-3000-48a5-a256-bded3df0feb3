import { environment } from "@/environments";
import {
  ListTasksCommand,
  DescribeTasksCommand,
  ECSClient,
} from "@aws-sdk/client-ecs";
import { fromSSO, fromEnv } from "@aws-sdk/credential-providers";

export async function fetchECSTaskIPs() {
  const client = getECSClient();
  const listECSCommand = new ListTasksCommand({
    cluster: environment.voteClusterName,
  });
  const listTasksCommandOutput = await client.send(listECSCommand);

  const taskArns = listTasksCommandOutput.taskArns;
  const describeTasksCommand = new DescribeTasksCommand({
    cluster: environment.voteClusterName,
    tasks: taskArns,
  });
  const describeTasksCommandOutput = await client.send(describeTasksCommand);

  const ipAddresses = describeTasksCommandOutput.tasks?.flatMap((task) =>
    task.attachments?.map(
      (a) => a.details?.find((d) => d.name === "privateIPv4Address")?.value,
    ),
  );

  return ipAddresses;
}

function getECSClient() {
  if (environment.local) {
    return new ECSClient({
      region: "ap-northeast-1",
      credentials: fromSSO(),
    });
  }

  return new ECSClient({
    region: "ap-northeast-1",
    credentials: fromEnv(),
  });
}
