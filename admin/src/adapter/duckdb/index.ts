import { environment } from "@/environments";
import { DuckDBConnection, DuckDBInstance } from "@duckdb/node-api";
import { headDumpedMessageTopic } from "../s3";
import { readFile } from "fs/promises";
import { logger } from "@/logger";
import { cache } from "react";

const getDuckDBInstance = cache(async () => {
  const ramdomId = Math.random().toString(36).substring(2, 15);
  const path = `/tmp/message_${ramdomId}.db`;
  const instance = await DuckDBInstance.create(path);
  logger.info({ path }, "createDB");
  const conn = await instance.connect();

  await conn.run(`
    SET home_directory = '/tmp';
    INSTALL aws;
    LOAD aws;
    INSTALL httpfs;
    LOAD httpfs;
    CREATE SECRET IF NOT EXISTS secret (
      TYPE s3,
      PROVIDER credential_chain,
      CHAIN 'sso;env'
    );`);

  return instance;
});

export async function fetchMessages(
  programId: string,
  topicId: string,
  filters: { [key: string]: string },
  sorting: { key: string; desc: boolean } | null,
  pagination: {
    pageIndex: number;
    pageSize: number;
  } = {
    pageIndex: 0,
    pageSize: 100,
  },
) {
  logger.info(
    "fetchMessages",
    programId,
    topicId,
    filters,
    sorting,
    pagination,
  );
  const table = await getTable(programId, topicId);
  if (table == null) {
    return null;
  }

  let query = "";
  if (Object.keys(filters).length > 0) {
    const filterClauses = Object.entries(filters).map(([key, value]) => {
      if (key === "id") {
        return `${key} = ${value}`;
      }
      return `${key} LIKE '%${value}%'`;
    });
    query = `WHERE ${filterClauses.join(" AND ")}`;
  }

  let orderBy = "ORDER BY id DESC";
  if (sorting) {
    const { key, desc } = sorting;
    orderBy = `ORDER BY ${key} ${desc ? "DESC" : "ASC"}`;
  }

  const prepared = await table.conn.prepare(
    `SELECT *
     FROM ${table.name} ${query}
      ${orderBy}
      LIMIT $1
      OFFSET $2;`,
  );

  prepared.bindInteger(1, pagination.pageSize);
  prepared.bindInteger(2, pagination.pageIndex * pagination.pageSize);

  const result = await prepared.run();
  const columnNames = result.columnNames();

  const chunks = await result.fetchAllChunks();
  const rows = chunks.flatMap((c) => c.getRows());

  const totalCountReader = await table.conn.runAndReadAll(`
    SELECT COUNT(*)
    FROM ${table.name} ${query}
  `);
  const totalCount = totalCountReader.getRows()[0][0];

  return { columnNames, rows, totalCount };
}

export async function dropMessageTopicTable(
  programId: string,
  topicId: string,
): Promise<void> {
  const table = await getTable(programId, topicId);
  if (table == null) {
    return;
  }

  await table.conn.run(`DROP TABLE ${table.name};`);
}

export async function fetchMessageTopicCSV(programId: string, topicId: string) {
  const table = await getTable(programId, topicId);
  if (table == null) {
    return null;
  }

  await table.conn.run(`
    COPY ${table.name} TO '/tmp/${programId}_${topicId}_output.csv' (DELIMITER ',');
  `);

  return await readFile(`/tmp/${programId}_${topicId}_output.csv`);
}

async function getTable(
  programId: string,
  topicId: string,
): Promise<{
  name: string;
  conn: DuckDBConnection;
} | null> {
  logger.info({ programId, topicId }, "getTable");
  const instance = await getDuckDBInstance();
  const conn = await instance.connect();

  const dataExists = await headDumpedMessageTopic(programId, topicId);
  if (!dataExists) {
    return null;
  }

  const tableName = `message_topic_${programId}_${topicId}`;
  const tableExists = await checkTableExists(conn, tableName);

  if (!tableExists) {
    logger.info({ tableName }, "Creating table");
    const path = `s3://${environment.messagesBucketName}/${programId}_${topicId}.jsonl.gz`;
    await conn.run(`
      CREATE TEMP TABLE temp_data AS SELECT * FROM read_json_auto('${path}');
      CREATE TABLE IF NOT EXISTS ${tableName} AS 
      SELECT 
        (ROW_NUMBER() OVER (ORDER BY sv_id)) AS id, 
        * 
      FROM temp_data 
      ORDER BY sv_id;
      DROP TABLE temp_data;
    `);
  }

  return { name: tableName, conn };
}

async function checkTableExists(
  conn: DuckDBConnection,
  tableName: string,
): Promise<boolean> {
  const result = await conn.runAndReadAll(`
    SELECT COUNT(*)
    FROM information_schema.tables
    WHERE table_name = '${tableName}'
      AND table_schema = 'main';
  `);
  const count = result.getRows()[0][0];

  switch (typeof count) {
    case "number":
      return count > 0;
    case "bigint":
      return Number(count) > 0;
    default:
      throw new Error("Invalid count type");
  }
}
