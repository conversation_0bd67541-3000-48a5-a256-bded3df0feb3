import { environment } from "@/environments";
import {
  DeleteObjectCommand,
  HeadObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";
import { fromEnv, fromSSO } from "@aws-sdk/credential-providers";

let client: S3Client | undefined;

function getS3Client() {
  if (client) {
    return client;
  }

  if (environment.local) {
    client = new S3Client({
      region: "ap-northeast-1",
      credentials: fromSSO(),
    });
  } else {
    client = new S3Client({
      region: "ap-northeast-1",
      credentials: fromEnv(),
    });
  }

  return client;
}

export async function headDumpedMessageTopic(
  programId: string,
  topicId: string,
) {
  const client = getS3Client();
  const command = new HeadObjectCommand({
    Bucket: environment.messagesBucketName,
    Key: `${programId}_${topicId}.jsonl.gz`,
  });

  try {
    await client.send(command);
    return true;
  } catch (e) {
    if (e instanceof Error && e.name === "NotFound") {
      return false;
    }

    throw e;
  }
}

export async function deleteMessageTopic(
  programId: string,
  topicId: string,
): Promise<void> {
  const client = getS3Client();
  const command = new DeleteObjectCommand({
    Bucket: environment.messagesBucketName,
    Key: `${programId}_${topicId}.jsonl.gz`,
  });

  await client.send(command);
}
