import { useRef, useEffect, useLayoutEffect, useState } from "react";

export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef(callback);

  // Remember the latest callback if it changes.
  useIsomorphicLayoutEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval.
  useEffect(() => {
    // Don't schedule if no delay is specified.
    // Note: 0 is a valid value for delay.
    if (delay === null) {
      return;
    }

    const id = setInterval(() => {
      savedCallback.current();
    }, delay);

    return () => {
      clearInterval(id);
    };
  }, [delay]);
}

const useIsomorphicLayoutEffect =
  typeof window !== "undefined" ? useLayoutEffect : useEffect;

export function useRepeatedPromise(
  callback: () => Promise<number>,
  interval: number,
  enabled: boolean,
) {
  useEffect(() => {
    if (!enabled) {
      return;
    }

    const repeater = new PromiseRepeater(callback);
    repeater.run(interval);

    return () => {
      repeater.dispose();
    };
  }, [callback, interval, enabled]);
}

class PromiseRepeater {
  private enabled = true;
  constructor(private callback: () => Promise<number>) {
    this.callback = callback;
  }

  run = async (interval: number) => {
    while (this.enabled) {
      const elapsed = await this.callback();
      const nextDelay = interval - elapsed;
      if (nextDelay > 0) {
        await new Promise((resolve) => setTimeout(resolve, nextDelay));
      }
    }
  };

  dispose = () => {
    this.enabled = false;
  };
}

export function useDebounce<T>(value: T, delay: number) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
