// https://github.com/milamer/parse-nested-form-data/blob/main/src/index.ts

export class DuplicateKeyError extends Error {
  key: string;
  constructor(key: string) {
    super(`Duplicate key at path part ${key}`);
    this.key = key;
  }
}

export class MixedArrayError extends Error {
  key: string;
  constructor(key: string) {
    super(`Mixed array at path part ${key}`);
    this.key = key;
  }
}

type JsonObject = { [Key in string]?: JsonValue };
type JsonArray = Array<JsonValue>;
type JsonValue =
  | string
  | number
  | JsonObject
  | JsonArray
  | boolean
  | null
  | File;
type JsonLeafValue = Exclude<JsonValue, JsonArray | JsonObject>;

function isJsonObject(val: JsonValue): val is JsonObject {
  return (
    typeof val === "object" &&
    !Array.isArray(val) &&
    val !== null &&
    !(val instanceof File)
  );
}

function defaultTransform(entry: [path: string, value: string | File]): {
  path: string;
  value: JsonLeafValue;
} {
  let path = entry[0];
  let value: JsonLeafValue = entry[1];
  if (path.startsWith("+")) {
    path = path.slice(1);
    value = Number(value);
  } else if (path.startsWith("&")) {
    path = path.slice(1);
    value = value === "on" || value === "true" || Boolean(Number(value));
  } else if (path.startsWith("-")) {
    path = path.slice(1);
    value = null;
  }
  return { path, value };
}

type DefaultTransform = typeof defaultTransform;

export type ParseFormDataOptions = {
  removeEmptyString?: boolean;
  transformEntry?: (
    entry: [path: string, value: string | File],
    defaultTransform: DefaultTransform,
  ) => { path: string; value: JsonLeafValue };
};

type PathPart = {
  path: string;
  type: "object" | "array";
  default: {} | [];
  pathToPart: string;
};

function extractPathParts(path: string): Array<PathPart> {
  const re = /((?<array>\d*)\]|(?<pathPart>[^.[]+))(?<nextType>\[|\.|$)/g;

  return Array.from(path.matchAll(re)).map<PathPart>((match) => {
    // self casted RegexExpMatchArray to custom
    const typedMatch = match as unknown as [string, string] &
      RegExpExecArray & {
        groups:
          | {
              pathPart: undefined;
              array: string;
              nextType: "[" | "." | "";
            }
          | {
              pathPart: string;
              array: undefined;
              nextType: "[" | "." | "";
            };
      };
    const { array, pathPart, nextType } = typedMatch.groups;
    const type = array === undefined ? "object" : "array";
    const nextDefault = nextType === "[" ? [] : {};
    return {
      path: array ?? pathPart,
      type,
      default: nextDefault,
      pathToPart: path.slice(0, typedMatch.index + typedMatch[1].length),
    };
  });
}

function handlePathPart(
  pathPart: PathPart,
  currentPathObject: JsonArray | JsonObject,
  arraysWithOrder: Set<JsonArray>,
): [
  nextPathValue: JsonValue | undefined,
  setNextPathValue: (value: JsonValue) => void,
] {
  if (pathPart.type === "object") {
    if (Array.isArray(currentPathObject)) {
      throw new DuplicateKeyError(pathPart.pathToPart);
    }
    const currentObject = currentPathObject;
    return [
      currentObject[pathPart.path],
      (val) => (currentObject[pathPart.path] = val),
    ];
  }
  if (!Array.isArray(currentPathObject)) {
    throw new DuplicateKeyError(pathPart.pathToPart);
  }
  const currentArray = currentPathObject;
  const isOrdered = pathPart.path !== "";

  const isOrderedArray = arraysWithOrder.has(currentArray);
  if (isOrdered) {
    arraysWithOrder.add(currentArray);
  }
  if (
    (!isOrdered && isOrderedArray) ||
    (isOrdered && !isOrderedArray && currentArray.length > 0)
  ) {
    throw new MixedArrayError(pathPart.pathToPart);
  }

  const order = isOrdered ? Number(pathPart.path) : currentArray.length;
  return [currentArray[order], (val) => (currentArray[order] = val)];
}

export function parseFormData(
  formData: Iterable<[string, string | File]>,
  {
    removeEmptyString = false,
    transformEntry = defaultTransform,
  }: ParseFormDataOptions = {},
): JsonObject {
  const result: JsonObject = {};

  // all arrays we need to squash (in place) later
  const arraysWithOrder: Set<Array<JsonValue>> = new Set();

  for (const entry of Array.from(formData)) {
    if (removeEmptyString && entry[1] === "") continue;

    const { path, value } = transformEntry(entry, defaultTransform);
    const pathParts = extractPathParts(path);

    let currentPathObject: JsonObject | JsonArray = result;
    pathParts.forEach((pathPart, idx) => {
      const [nextPathValue, setNextPathValue] = handlePathPart(
        pathPart,
        currentPathObject,
        arraysWithOrder,
      );

      if (pathParts.length - 1 === idx) {
        if (nextPathValue !== undefined) {
          throw new DuplicateKeyError(pathPart.pathToPart);
        }
        setNextPathValue(value);
      } else {
        if (
          nextPathValue !== undefined &&
          !isJsonObject(nextPathValue) &&
          !Array.isArray(nextPathValue)
        ) {
          throw new DuplicateKeyError(pathPart.pathToPart);
        }

        const nextPathObject = nextPathValue ?? pathPart.default;
        currentPathObject = nextPathObject;
        setNextPathValue(nextPathObject);
      }
    });
  }

  for (const orderedArray of Array.from(arraysWithOrder)) {
    // replace array with a squashed array
    // array.flat(0) will remove all empty slots (e.g. [0, , 1] => [0, 1])
    orderedArray.splice(0, orderedArray.length, ...orderedArray.flat(0));
  }

  return result;
}
