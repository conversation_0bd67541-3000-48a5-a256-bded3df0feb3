import { logger } from "./logger";

export const environment = {
  local: !!process.env.LOCAL,
  envName: process.env.ENV_NAME,
  voteTableName: process.env.VOTE_TABLE_NAME,
  voteTableIdTypeIndexName: process.env.VOTE_TABLE_ID_TYPE_INDEX_NAME,
  cmsTableName: process.env.CMS_TABLE_NAME,
  cmsTableIdTypeIndexName: process.env.CMS_TABLE_ID_TYPE_INDEX_NAME,
  cmsTableTypeEndTimeIndexName: process.env.CMS_TABLE_TYPE_END_TIME_INDEX_NAME,
  cmsTableUserIdIdIndexName: process.env.CMS_TABLE_USER_ID_ID_INDEX_NAME,
  messagesBucketName: process.env.MESSAGES_BUCKET_NAME,
  counterVpcBridgeFunctionName: process.env.COUNTER_VPC_BRIDGE_FUNCTION_NAME,
  voteClusterName: process.env.VOTE_CLUSTER_NAME,
  helperFunctionName: process.env.HELPER_FUNCTION_NAME,
  r7ApiBaseURL: process.env.R7_API_BASE_URL,
  backupPassword: process.env.BACKUP_PASSWORD,
  nextAuthURL: process.env.NEXT_AUTH_URL,
};

if (!environment.local) {
  const maskedPasswrod =
    environment.backupPassword != null
      ? `${environment.backupPassword.slice(0, 3)}...${environment.backupPassword.slice(-3)}`
      : "undefined";
  logger.info(
    {
      ...environment,
      // show only first and last 3 characters of the password
      backupPassword: maskedPasswrod,
    },
    "environment",
  );
}
