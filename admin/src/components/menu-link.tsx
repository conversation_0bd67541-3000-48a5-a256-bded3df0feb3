"use client";

import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { buttonVariants } from "./ui/button";
import Link from "next/link";

export function MenuLink(props: { href: string; children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <Link
      href={props.href}
      className={menuLinkClassname(pathname.startsWith(props.href))}
    >
      {props.children}
    </Link>
  );
}

function menuLinkClassname(isActive: boolean) {
  return cn(
    buttonVariants({ variant: "ghost" }),
    isActive ? "justify-start rounded border-2" : "justify-start",
  );
}
