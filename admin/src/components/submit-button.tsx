import { Loader2 } from "lucide-react";
import { useFormStatus } from "react-dom";
import { Button } from "./ui/button";
import { ReactNode } from "react";

interface Props {
  children?: ReactNode;
  pendingLabel?: string;
}

export function SubmitButton(props: Props) {
  const status = useFormStatus();

  return status.pending ? (
    <Button type="submit" disabled>
      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      {props.pendingLabel || "保存しています"}
    </Button>
  ) : (
    <Button type="submit">{props.children || "保存する"}</Button>
  );
}
