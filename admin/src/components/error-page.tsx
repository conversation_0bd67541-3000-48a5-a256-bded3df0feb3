import { FileWarningIcon } from "lucide-react";
import Link from "next/link";
import { Button, buttonVariants } from "./ui/button";

export function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="mx-auto w-full rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
        <div className="flex flex-col items-center justify-center gap-4 text-center">
          <FileWarningIcon className="h-16 w-16 text-red-500" />
          <h2 className="mt-6 text-3xl font-bold text-gray-800 dark:text-gray-200">
            エラーが発生しました
          </h2>
          <h3>エラーメッセージ</h3>
          <div className="w-full max-w-xl flex-col items-start rounded-md bg-gray-100 p-4 text-left dark:bg-black">
            <div className="font-mono">{error.message}</div>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={reset}>戻る</Button>
            <Link className={buttonVariants()} href="/">
              ホームに戻る
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
