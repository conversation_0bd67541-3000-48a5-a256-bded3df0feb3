import { cookies } from "next/headers";
import { FlashMessageRenderer } from "./flash-message-renderer";

// cookieのフィールド名にtoastプレフィックスがついた値使ってトーストを表示する
export async function FlashMessage() {
  const cookieStore = await cookies();
  const title = cookieStore.get(toastKey.title)?.value;
  const message = cookieStore.get(toastKey.message)?.value;
  const variant = cookieStore.get(toastKey.variant)?.value as
    | "default"
    | "destructive"
    | null;
  const id = cookieStore.get(toastKey.id)?.value;

  return (
    <FlashMessageRenderer
      title={title}
      message={message}
      variant={variant}
      id={id}
    />
  );
}

export async function setFlashMessageCookie({
  title,
  message,
  id,
  variant,
}: {
  title?: string;
  message?: string;
  id?: string;
  variant?: "default" | "destructive" | null;
}) {
  const cookieStore = await cookies();
  if (title) {
    cookieStore.set(toastKey.title, title, { maxAge: 1 });
  }
  if (message) {
    cookieStore.set(toastKey.message, message, { maxAge: 1 });
  }
  if (variant) {
    cookieStore.set(toastKey.variant, variant, { maxAge: 1 });
  }

  // idを指定していない場合はユニークなIDを生成して重複しないようにする
  cookieStore.set(toastKey.id, id ?? new Date().toISOString(), { maxAge: 1 });
}

export const toastKey = {
  title: "toast-title",
  message: "toast-message",
  variant: "toast-variant",
  id: "toast-id", // 同じtitle, messageが連続してもトーストを仕組み上出せないのでその場合はidを変える
};
