"use client";

import * as React from "react";
import { CalendarIcon, XIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button, buttonVariants } from "@/components/ui/button";
import { Calendar, CalendarProps } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import dayjs from "dayjs";

interface DatePickerProps {
  date: Date | undefined;
  onSelect: (date: Date | undefined) => void;
  showClearButton?: boolean;
  className?: string;
  calendarProps?: CalendarProps;
}

export function DatePicker(props: DatePickerProps) {
  return (
    <Popover>
      <div
        className={cn(
          buttonVariants({ variant: "outline" }),
          "relative w-[280px] px-0",
          props.className,
        )}
      >
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start text-left font-normal",
              !props.date && "text-muted-foreground",
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {props.date ? (
              dayjs(props.date).format("YYYY/M/D")
            ) : (
              <span>日付を指定する</span>
            )}
          </Button>
        </PopoverTrigger>
        {props.date && props.showClearButton ? (
          <Button
            variant="ghost"
            className="absolute right-0 top-0 px-2"
            onClick={() => props.onSelect(undefined)}
          >
            <XIcon className="h-4 w-4" />
          </Button>
        ) : null}
      </div>
      <PopoverContent className="w-auto p-0">
        <Calendar
          {...props.calendarProps}
          mode="single"
          selected={props.date}
          onSelect={props.onSelect}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}
