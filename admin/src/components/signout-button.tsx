"use client";

import {
  Too<PERSON><PERSON><PERSON>rov<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
  TooltipContent,
} from "@radix-ui/react-tooltip";
import { LogOut } from "lucide-react";
import { signOut } from "next-auth/react";
import { Button } from "./ui/button";
import { addLogoutHistoryAction } from "./actions";

export function SignOutButton() {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <form
            action={async () => {
              await addLogoutHistoryAction();
              signOut();
            }}
          >
            <Button variant="ghost" type="submit">
              <LogOut />
            </Button>
          </form>
        </TooltipTrigger>
        <TooltipContent className="rounded-lg bg-gray-300 p-4 dark:bg-gray-800">
          <p>ログアウトする</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
