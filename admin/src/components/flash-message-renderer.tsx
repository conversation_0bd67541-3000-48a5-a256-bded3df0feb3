"use client";

import { useEffect } from "react";
import { useToast } from "./ui/use-toast";

interface Props {
  title?: string;
  message?: string;
  id?: string;
  variant?: "default" | "destructive" | null;
}

export function FlashMessageRenderer({ title, message, id, variant }: Props) {
  const { toast } = useToast();

  useEffect(() => {
    if (message || title) {
      toast({ title, description: message, variant });
    }
  }, [id, title, message, variant, toast]);

  return null;
}
