"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AggAPIAccessTokenRecord } from "@/adapter/dynamodb/agg-api-access-token";
import { Program } from "@/adapter/dynamodb/program";
import AccessTokenDetail from "./token-detail";

interface TokenDetailDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  token: AggAPIAccessTokenRecord;
  program: Program | null;
}

export default function TokenDetailDialog({
  isOpen,
  onOpenChange,
  token,
  program,
}: TokenDetailDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[640px]">
        <DialogHeader>
          <DialogTitle>アクセストークン</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <AccessTokenDetail token={token} program={program} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
