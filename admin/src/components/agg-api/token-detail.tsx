"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, EyeOff, ClipboardCopy, AlertCircle } from "lucide-react";
import { AggAPIAccessTokenRecord } from "@/adapter/dynamodb/agg-api-access-token";
import { Program } from "@/adapter/dynamodb/program";
import { DeleteAccessTokenButton } from "./delete-access-token-button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { ExpirationEdit } from "./expiration-edit";
import dayjs from "dayjs";
import { Alert, AlertTitle, AlertDescription } from "../ui/alert";

const dummyToken = "••••••••••••••••••••••••••••••••";

interface AccessTokenDetailProps {
  token: AggAPIAccessTokenRecord;
  program: Program | null;
}

export default function AccessTokenDetail({
  token,
  program,
}: AccessTokenDetailProps) {
  const [showToken, setShowToken] = useState(false);
  const { toast } = useToast();

  let isExpired;
  if (token.Data.expiresAt) {
    const expiresAt = dayjs(token.Data.expiresAt);
    if (expiresAt.isBefore(dayjs())) {
      isExpired = "already" as const;
    } else if (expiresAt.isBefore(dayjs().add(1, "month"))) {
      isExpired = "soon" as const;
    }
  }

  return (
    <div>
      <div className="space-y-4 *:space-y-2">
        {isExpired && (
          <Alert variant="destructive" className="-mt-4 dark:text-red-500">
            <AlertCircle className="mt-2 h-4 w-4" />
            <AlertTitle>
              このアクセストークンは{isExpired === "soon" ? "まもなく" : ""}
              期限切れです
            </AlertTitle>
            <AlertDescription>
              アクセストークンを削除するか、有効期限を延長してください
            </AlertDescription>
          </Alert>
        )}
        <div>
          <h3 className="text-lg font-semibold">名前</h3>
          <p>{token.Data.name}</p>
        </div>
        <div>
          <h3 className="text-lg font-semibold">対象番組</h3>
          <p>{program?.Data.name ?? "なし（全番組アクセス可能）"}</p>
        </div>
        {program == null && (
          <div>
            <h3 className="text-lg font-semibold">有効期限</h3>
            <ExpirationEdit token={token} />
          </div>
        )}
        <div>
          <h3 className="text-lg font-semibold">アクセストークン</h3>
          <div className="flex items-center space-x-2">
            <div className="flex-1 rounded bg-gray-100 p-2 font-mono dark:bg-gray-900">
              {showToken ? token.Data.token : dummyToken}
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowToken(!showToken)}
                  >
                    {showToken ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {showToken
                        ? "アクセストークンを隠す"
                        : "アクセストークンを表示"}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {showToken
                      ? "アクセストークンを隠す"
                      : "アクセストークンを表示"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      navigator.clipboard.writeText(token.Data.token);
                      toast({
                        description: "アクセストークンをコピーしました",
                      });
                    }}
                  >
                    <ClipboardCopy className="h-4 w-4" />
                    <span className="sr-only">
                      アクセストークンをクリップボードにコピー
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>アクセストークンをクリップボードにコピー</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <p className={cn("text-sm leading-6", !showToken && "invisible")}>
            集計APIへリクエストを送信する際にアクセストークンを
            <span className="mx-2 rounded bg-gray-100 p-2 font-mono dark:bg-gray-800">
              Autorization: Bearer {showToken ? token.Data.token : dummyToken}
            </span>
            のようにHTTPヘッダに設定してください
          </p>
        </div>
      </div>
      <div className="mt-4 flex justify-end">
        <DeleteAccessTokenButton token={token} />
      </div>
    </div>
  );
}
