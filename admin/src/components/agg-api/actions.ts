"use server";

import {
  createAgg<PERSON><PERSON>ccessToken,
  saveAggAPIAccessToken,
} from "@/adapter/dynamodb/agg-api-access-token";
import { getLoginUser } from "@/auth";
import { setFlashMessageCookie } from "@/components/flash-message";
import { checkbox } from "@/utils/form-schema";
import {
  deleteAggAPIAccessToken,
  fetchAggAPIAccessToken,
} from "@/adapter/dynamodb/agg-api-access-token";
import { getTokenProgramId } from "@/adapter/dynamodb/util-client";
import { z } from "zod";

const CreateAggAPIAccessTokenFormSchema = z.object({
  programId: z.optional(z.string()),
  allProgramId: checkbox(),
  expiresAt: z.string().date().optional(),
  name: z.string(),
});

export async function createAggAPIAccessTokenAction(formData: FormData) {
  const loginUser = await getLoginUser();
  const data = Object.fromEntries(formData.entries());
  const parsed = CreateAggAPIAccessTokenFormSchema.parse(data);

  if (loginUser.isAdmin) {
    if (parsed.allProgramId && !parsed.expiresAt) {
      return "expires_at_required";
    }

    await createAggAPIAccessToken(
      parsed.name,
      parsed.allProgramId ? undefined : parsed.programId,
      parsed.expiresAt,
    );
  } else {
    if (parsed.allProgramId) {
      return "permission_denied";
    }
    if (parsed.programId == null) {
      return "program_id_required";
    }

    await createAggAPIAccessToken(parsed.name, parsed.programId);
  }

  await loginUser.addHistory({
    kind: "aggApiToken#create",
    name: parsed.name,
    programId: parsed.programId,
  });

  await setFlashMessageCookie({
    title: "集計APIのアクセス権を作成しました",
  });

  return "ok";
}

const DeleteAccessTokenSchema = z.object({
  tokenId: z.string(),
});

export async function deleteAccessTokenAction(formData: FormData) {
  const data = Object.fromEntries(formData.entries());
  const { tokenId } = DeleteAccessTokenSchema.parse(data);
  const token = await fetchAggAPIAccessToken(tokenId);
  if (token == null) {
    throw new Error("token_not_found");
  }

  const tokenProgramId = getTokenProgramId(token);

  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    if (tokenProgramId == null) {
      // 一般ユーザーは対象番組を持たないトークンを削除できない
      throw new Error("permission_denied");
    }

    if (!loginUser.hasAccessToProgram(tokenProgramId)) {
      throw new Error("permission_denied");
    }
  }

  await deleteAggAPIAccessToken(tokenId);
  await loginUser.addHistory({
    kind: "aggApiToken#delete",
    tokenId: tokenId,
    programId: tokenProgramId,
    name: token.Data.name,
  });

  await setFlashMessageCookie({
    title: "アクセス権を削除しました",
  });
}

const UpdateAccessTokenExpirationSchema = z.object({
  tokenId: z.string(),
  expiresAt: z.string().date(),
});

export async function updateAccessTokenExpirationAction(formData: FormData) {
  const data = Object.fromEntries(formData.entries());
  const { tokenId, expiresAt } = UpdateAccessTokenExpirationSchema.parse(data);

  const loginUser = await getLoginUser();
  if (!loginUser.isAdmin) {
    throw new Error("permission_denied");
  }

  const token = await fetchAggAPIAccessToken(tokenId);
  if (token == null) {
    throw new Error("token_not_found");
  }

  await saveAggAPIAccessToken(tokenId, token.Data.name, expiresAt);

  await loginUser.addHistory({
    kind: "aggApiToken#update-expires-at",
    programId: getTokenProgramId(token),
    name: token.Data.name,
  });

  await setFlashMessageCookie({
    title: "アクセス権の期限を更新しました",
  });

  return "ok";
}
