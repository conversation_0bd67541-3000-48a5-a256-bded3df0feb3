"use client";

import Form from "next/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useActionState, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { createAggAPIAccessTokenAction } from "./actions";
import { Program } from "@/adapter/dynamodb/program";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { DatePicker } from "@/components/date-picker";
import dayjs from "dayjs";

export interface CreateAggAPIAccessTokenButtonProps {
  isAdmin: boolean;
  programs: Program[];
}

export function CreateAggAPIAccessTokenButton(
  props: CreateAggAPIAccessTokenButtonProps,
) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isAllProgramId, setIsAllProgramId] = useState(false);
  const [expiresAt, setExpiresAt] = useState<Date | undefined>(undefined);
  const [programId, setProgramId] = useState<string | undefined>(undefined);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      if (programId != null) {
        formData.append("programId", programId);
      } else {
        if (expiresAt == null) {
          toast({
            variant: "destructive",
            description: "番組を限定しないアクセストークンは有効期限が必須です",
          });
          return;
        }

        formData.append("expiresAt", dayjs(expiresAt).format("YYYY-MM-DD"));
      }

      const result = await createAggAPIAccessTokenAction(formData);
      switch (result) {
        case "ok":
          setIsOpen(false);
          break;
        case "permission_denied":
          toast({
            variant: "destructive",
            description: "権限がありません",
          });
          break;
        case "program_id_required":
          toast({
            variant: "destructive",
            description: "番組を選択してください",
          });
          break;
        case "expires_at_required":
          toast({
            variant: "destructive",
            description: "番組を限定しないアクセストークンは有効期限が必須です",
          });
          break;
        default: {
          const _: never = result;
        }
      }
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        集計APIのアクセス権を作成する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>集計APIのアクセス権を作成する</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {props.isAdmin ? (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="flex justify-end">
                    <Checkbox
                      id="allProgramId"
                      name="allProgramId"
                      className="text-right"
                      checked={isAllProgramId}
                      onClick={() => setIsAllProgramId((prev) => !prev)}
                    />
                  </div>
                  <Label htmlFor="allProgramId" className="col-span-3">
                    番組を限定しないアクセストークンを作成する
                  </Label>
                </div>
              ) : null}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  名前
                </Label>
                <Input id="name" name="name" required className="col-span-3" />
              </div>
              {isAllProgramId ? (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="expiresAt" className="text-right">
                    有効期限
                  </Label>
                  <DatePicker
                    className="col-span-3 w-full"
                    date={expiresAt}
                    onSelect={(date) => setExpiresAt(date)}
                    calendarProps={{
                      toDate: dayjs().add(1, "year").add(6, "month").toDate(),
                      required: true,
                    }}
                  />
                </div>
              ) : (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="programId" className="text-right">
                    番組名
                  </Label>
                  <Select
                    disabled={isAllProgramId}
                    onValueChange={setProgramId}
                    required={!props.isAdmin || !isAllProgramId}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="番組を選択" />
                    </SelectTrigger>
                    <SelectContent id="programId">
                      <SelectGroup>
                        <SelectLabel>番組名</SelectLabel>
                        {props.programs.map((p) => (
                          <SelectItem key={p.ID} value={p.ID}>
                            {p.Data.name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button type="submit" variant="default" disabled={isPending}>
                作成する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
