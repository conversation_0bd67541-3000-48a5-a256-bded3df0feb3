"use client";

import { useActionState, useState } from "react";
import Form from "next/form";
import dayjs from "dayjs";
import { AggAPIAccessTokenRecord } from "@/adapter/dynamodb/agg-api-access-token";
import { DatePicker } from "../date-picker";
import { Button } from "../ui/button";
import { updateAccessTokenExpirationAction } from "./actions";

interface ExpirationEditProps {
  token: AggAPIAccessTokenRecord;
}

export function ExpirationEdit({ token }: ExpirationEditProps) {
  const [expiresAt, setExpiresAt] = useState<Date | undefined>(
    token.Data.expiresAt ? new Date(token.Data.expiresAt) : undefined,
  );

  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async () => {
      const formData = new FormData();
      formData.append("tokenId", token.ID);
      const date = expiresAt ? dayjs(expiresAt).format("YYYY-MM-DD") : "";
      formData.append("expiresAt", date);
      await updateAccessTokenExpirationAction(formData);
    },
    {},
  );

  return (
    <div className="flex justify-between">
      <DatePicker date={expiresAt} onSelect={setExpiresAt} />
      <div>
        <Form action={submitAction}>
          <Button
            variant="secondary"
            onClick={() =>
              setExpiresAt(
                token.Data.expiresAt
                  ? new Date(token.Data.expiresAt)
                  : undefined,
              )
            }
            type="button"
          >
            リセット
          </Button>
          <Button variant="default" type="submit" disabled={isPending}>
            更新する
          </Button>
        </Form>
      </div>
    </div>
  );
}
