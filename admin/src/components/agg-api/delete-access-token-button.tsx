"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useActionState, useState } from "react";
import Form from "next/form";
import { deleteAccessTokenAction } from "./actions";
import { AggAPIAccessTokenRecord } from "@/adapter/dynamodb/agg-api-access-token";

export function DeleteAccessTokenButton(props: {
  token: AggAPIAccessTokenRecord;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (_prev, formData) => {
      await deleteAccessTokenAction(formData);
      setIsOpen(false);
    },
    {},
  );

  return (
    <>
      <Button onClick={() => setIsOpen(true)} variant="destructive">
        アクセス権を削除する
      </Button>
      <Dialog open={isOpen} onOpenChange={(v) => setIsOpen(v)}>
        <DialogContent>
          <Form action={submitAction}>
            <DialogHeader>
              <DialogTitle>アクセス権を削除する</DialogTitle>
              <DialogDescription className="py-4 text-black">
                アクセス権を削除します。 この操作はやり直せません
              </DialogDescription>
            </DialogHeader>
            <input
              type="hidden"
              id="tokenId"
              name="tokenId"
              value={props.token.ID}
            />
            <DialogFooter>
              <Button type="submit" variant="destructive" disabled={isPending}>
                削除する
              </Button>
            </DialogFooter>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
