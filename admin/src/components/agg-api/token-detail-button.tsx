"use client";

import { useState } from "react";
import { AggAPIAccessTokenRecord } from "@/adapter/dynamodb/agg-api-access-token";
import { Program } from "@/adapter/dynamodb/program";
import { Button } from "@/components/ui/button";
import TokenDetailDialog from "./token-detail-dialog";

interface TokenDetailButtonProps {
  token: AggAPIAccessTokenRecord;
  program: Program | null;
}

export function TokenDetailButton({ token, program }: TokenDetailButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button variant="secondary" onClick={() => setIsOpen(true)}>
        表示
      </Button>
      <TokenDetailDialog
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        token={token}
        program={program}
      />
    </>
  );
}
