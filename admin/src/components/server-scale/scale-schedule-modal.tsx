"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Form from "next/form";
import { putScaleScheduleAction } from "./actions";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { weekDays } from "@/lib/consts";
import { ScaleSchedule } from "@/adapter/dynamodb/scale-schedule";
import { useActionState } from "react";
import { ScaleScheduleDeleteButton } from "./scale-schedule-delete-button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Program } from "@/adapter/dynamodb/program";

interface ScaleScheduleModalProps {
  schedule: ScaleSchedule | null;
  programs: Program[];
  programId?: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function ScaleScheduleModal(props: ScaleScheduleModalProps) {
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      const result = await putScaleScheduleAction(formData);
      if (result === "ok") {
        props.onOpenChange(false);
      }
    },
    {},
  );
  return (
    <Dialog open={props.isOpen} onOpenChange={props.onOpenChange}>
      <DialogContent>
        <Form action={submitAction}>
          {props.schedule && (
            <input type="hidden" name="id" value={props.schedule.ID} />
          )}
          <DialogHeader>
            <DialogTitle>
              繰り返し予約を{props.schedule ? "編集" : "作成"}
              する
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="space-y-4 *:space-y-2">
            <div>
              <Label htmlFor="name">名前</Label>
              <Input
                id="name"
                name="name"
                required
                defaultValue={props.schedule?.Data.name}
              />
            </div>
            {props.programId ? (
              <input type="hidden" name="programId" value={props.programId} />
            ) : (
              <div>
                <Label htmlFor="programId">番組</Label>
                <Select
                  name="programId"
                  required
                  disabled={props.programs.length === 0}
                  defaultValue={props.schedule?.Data.programId}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="番組" />
                  </SelectTrigger>
                  <SelectContent>
                    {props.programs.map((p) => (
                      <SelectItem key={p.ID} value={p.ID}>
                        {p.Data.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {props.programs.length === 0 && (
                  <p className="text-xs text-red-500">
                    担当番組がありません。繰り返し予約を作成するには担当番組が必要です。
                  </p>
                )}
              </div>
            )}
            <div>
              <Label htmlFor="expectedMaxRps">想定最大RPS</Label>
              <Input
                id="expectedMaxRps"
                name="expectedMaxRps"
                type="number"
                required
                step="10000"
                min="10000"
                max="200000"
                defaultValue={props.schedule?.Data.expectedMaxRps}
              />
            </div>
            <div>
              <Label htmlFor="weekDays">曜日</Label>
              <div className="flex flex-col gap-2">
                {weekDays.map((day) => (
                  <div key={day.value} className="flex items-center gap-2">
                    <Checkbox
                      id={day.value}
                      name={day.value}
                      defaultChecked={props.schedule?.Data.weekDays.includes(
                        day.value,
                      )}
                    />
                    <Label htmlFor={day.value}>{day.label}曜日</Label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <Label htmlFor="startTime">開始時刻</Label>
              <Input
                id="startTime"
                name="startTime"
                type="time"
                required
                className="w-fit"
                defaultValue={props.schedule?.Data.startAt}
              />
            </div>
            <div>
              <Label htmlFor="endTime">終了時刻</Label>
              <Input
                id="endTime"
                name="endTime"
                type="time"
                required
                className="w-fit"
                defaultValue={props.schedule?.Data.endAt}
              />
            </div>
          </div>
          <DialogFooter className="pt-4">
            {props.schedule && (
              <ScaleScheduleDeleteButton
                id={props.schedule.ID}
                onDelete={() => props.onOpenChange(false)}
              />
            )}
            <Button type="submit" disabled={isPending}>
              保存する
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
