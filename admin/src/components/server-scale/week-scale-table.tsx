"use client";

import { ScaleSchedule } from "@/adapter/dynamodb/scale-schedule";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScaleScheduleModal } from "./scale-schedule-modal";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Program } from "@/adapter/dynamodb/program";
import Link from "next/link";
import { LinkIcon } from "lucide-react";

export function WeekScaleScheduleTable({
  schedules,
  programs,
  programId,
}: {
  schedules: ScaleSchedule[];
  programs: Program[];
  programId?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<ScaleSchedule | null>(
    null,
  );

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>予約名</TableHead>
            <TableHead>曜日</TableHead>
            <TableHead>開始時刻</TableHead>
            <TableHead>終了時刻</TableHead>
            {!programId && <TableHead>番組名</TableHead>}
            <TableHead>想定最大RPS</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {schedules.map((schedule) => (
            <TableRow key={schedule.ID}>
              <TableCell>{schedule.Data.name}</TableCell>
              <TableCell>{printWeekdays(schedule.Data.weekDays)}</TableCell>
              <TableCell>{schedule.Data.startAt}</TableCell>
              <TableCell>{schedule.Data.endAt}</TableCell>
              {!programId && (
                <TableCell>
                  {
                    programs.find((p) => p.ID === schedule.Data.programId)?.Data
                      .name
                  }
                </TableCell>
              )}
              <TableCell>{schedule.Data.expectedMaxRps}</TableCell>
              <TableCell className="flex justify-end">
                {programId ? (
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setEditingSchedule(schedule);
                      setIsOpen(true);
                    }}
                  >
                    編集
                  </Button>
                ) : (
                  <Link
                    href={`/programs/${schedule.Data.programId}`}
                    className="flex items-center gap-2"
                  >
                    番組詳細
                    <LinkIcon className="h-4 w-4" />
                  </Link>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {schedules.length === 0 && (
        <p className="text-sm">繰り返し予約はありません</p>
      )}
      {programId && (
        <div className="flex justify-end">
          <Button
            onClick={() => {
              setEditingSchedule(null);
              setIsOpen(true);
            }}
          >
            繰り返し予約を作成する
          </Button>
        </div>
      )}
      <ScaleScheduleModal
        isOpen={isOpen}
        onOpenChange={(isOpen) => setIsOpen(isOpen)}
        schedule={editingSchedule}
        programs={programs}
        programId={programId}
      />
    </div>
  );
}

function printWeekdays(weekdays: string[]) {
  return weekdays
    .map((weekday) => {
      switch (weekday) {
        case "mon":
          return "月";
        case "tue":
          return "火";
        case "wed":
          return "水";
        case "thu":
          return "木";
        case "fri":
          return "金";
        case "sat":
          return "土";
        case "sun":
          return "日";
        default:
          return weekday;
      }
    })
    .join(", ");
}
