import {
  Program,
  fetchProgram,
  fetchPrograms,
} from "@/adapter/dynamodb/program";
import { fetchScalesAfter } from "@/adapter/dynamodb/scale";
import {
  ScaleSchedule,
  fetchScaleSchedules,
} from "@/adapter/dynamodb/scale-schedule";
import { LoginUser } from "@/auth";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

export async function fetchSchedulesByUser(
  loginUser: LoginUser,
  programId?: string,
): Promise<ScaleSchedule[]> {
  let schedules = await fetchScaleSchedules();

  if (programId) {
    schedules = schedules.filter(
      (schedule) => schedule.Data.programId === programId,
    );
  } else if (!loginUser.isAdmin) {
    schedules = schedules.filter((schedule) =>
      loginUser.permissions.some(
        (permission) => permission.ID === schedule.Data.programId,
      ),
    );
  }

  return schedules;
}

// 今日以降のスケールを取得
export async function fetchUpcomingScalesByPermission(
  loginUser: LoginUser,
  programId?: string,
) {
  const today = dayjs().hour(0).minute(0).second(0).millisecond(0);
  const scales = await fetchScalesAfter(today);

  if (programId) {
    return scales.filter((s) => s.Data.programId === programId);
  }

  return loginUser.isAdmin
    ? scales
    : scales.filter((s) =>
        loginUser.permissions.some((p) => p.ID === s.Data.programId),
      );
}

export async function fetchProgramsByUser(
  loginUser: LoginUser,
  programId?: string,
): Promise<Program[]> {
  if (programId) {
    const program = await fetchProgram(programId);
    if (!program) {
      throw new Error("Program not found");
    }
    return [program];
  }

  const programs = await fetchPrograms();

  if (loginUser.isAdmin) {
    return programs;
  }

  return programs.filter((program) =>
    loginUser.permissions.some((permission) => permission.ID === program.ID),
  );
}
