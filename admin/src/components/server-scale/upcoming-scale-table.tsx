"use client";

import { useState } from "react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Form from "next/form";
import { putScaleAction } from "./actions";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useActionState } from "react";
import { Scale } from "@/adapter/dynamodb/scale";
import { ScaleDeleteButton } from "./scale-detele-button";
import { Program } from "@/adapter/dynamodb/program";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Table,
} from "@/components/ui/table";
import Link from "next/link";
import { LinkIcon } from "lucide-react";

dayjs.extend(utc);
dayjs.extend(timezone);

export function UpcomingScaleTable({
  programId,
  programs,
  scales,
}: {
  programId?: string;
  programs: Program[];
  scales: Scale[];
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedScale, setSelectedScale] = useState<Scale | null>(null);

  const sortedScales = scales.toSorted((a, b) => {
    if (a.StartTime === b.StartTime) {
      return a.EndTime < b.EndTime ? -1 : 1;
    }
    return a.StartTime < b.StartTime ? -1 : 1;
  });

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>予約名</TableHead>
            <TableHead>開始時刻</TableHead>
            <TableHead>終了時刻</TableHead>
            {!programId && <TableHead>番組名</TableHead>}
            <TableHead>想定最大RPS</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedScales.map((scale) => (
            <TableRow key={scale.ID}>
              <TableCell>{scale.Data.name}</TableCell>
              <TableCell>
                {dayjs(scale.StartTime)
                  .tz("Asia/Tokyo")
                  .format("YYYY/MM/DD HH:mm:ss")}
              </TableCell>
              <TableCell>
                {dayjs(scale.EndTime)
                  .tz("Asia/Tokyo")
                  .format("YYYY/MM/DD HH:mm:ss")}
              </TableCell>
              {!programId && (
                <TableCell>
                  {
                    programs.find((p) => p.ID === scale.Data.programId)?.Data
                      .name
                  }
                </TableCell>
              )}
              <TableCell>{scale.Data.expectedMaxRps}</TableCell>
              <TableCell className="flex justify-end">
                {programId ? (
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setSelectedScale(scale);
                      setIsOpen(true);
                    }}
                  >
                    編集
                  </Button>
                ) : (
                  <Link
                    href={`/programs/${scale.Data.programId}`}
                    className="flex items-center gap-2"
                  >
                    番組詳細
                    <LinkIcon className="h-4 w-4" />
                  </Link>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {scales.length === 0 && <p className="text-sm">確定予約はありません</p>}
      {programId && (
        <div className="flex justify-end">
          <Button
            onClick={() => {
              setSelectedScale(null);
              setIsOpen(true);
            }}
          >
            スポット予約を作成する
          </Button>
        </div>
      )}
      <ScaleModal
        scale={selectedScale}
        programs={programs}
        programId={programId}
        isOpen={isOpen}
        onOpenChange={setIsOpen}
      />
    </>
  );
}

interface ScaleModalProps {
  // scaleがあるときは編集、ないときは新規作成
  scale: Scale | null;
  // ユーザーの担当番組
  programs: Program[];
  // 番組管理画面から開く時はその番組ID
  programId?: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

function ScaleModal(props: ScaleModalProps) {
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      const result = await putScaleAction(formData);
      if (result === "ok") {
        props.onOpenChange(false);
      }
    },
    {},
  );

  const now = dayjs(props.scale?.StartTime).format("YYYY-MM-DDTHH:mm");
  const end = dayjs(props.scale?.EndTime)
    .add(props.scale ? 0 : 1, "hour")
    .format("YYYY-MM-DDTHH:mm");

  return (
    <Dialog open={props.isOpen} onOpenChange={props.onOpenChange}>
      <DialogContent>
        <Form action={submitAction}>
          {props.scale && (
            <input
              type="hidden"
              id="scaleId"
              name="scaleId"
              value={props.scale.ID}
            />
          )}
          <DialogHeader>
            <DialogTitle>
              スポット予約を{props.scale ? "編集" : "作成"}する
            </DialogTitle>
            <DialogDescription></DialogDescription>
          </DialogHeader>
          <div className="space-y-4 *:space-y-2">
            <div>
              <Label htmlFor="name">名前</Label>
              <Input
                id="name"
                name="name"
                required
                defaultValue={props.scale?.Data.name}
              />
            </div>
            {props.programId ? (
              <input type="hidden" name="programId" value={props.programId} />
            ) : (
              <div>
                <Label htmlFor="programId">番組</Label>
                <Select
                  name="programId"
                  required
                  disabled={props.programs.length === 0}
                  defaultValue={props.scale?.Data.programId}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="番組" />
                  </SelectTrigger>
                  <SelectContent>
                    {props.programs.map((p) => (
                      <SelectItem key={p.ID} value={p.ID}>
                        {p.Data.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {props.programs.length === 0 && (
                  <p className="text-xs text-red-500">
                    担当番組がありません。スポット予約を作成するには担当番組が必要です。
                  </p>
                )}
              </div>
            )}
            <div>
              <Label htmlFor="startTime">開始時間</Label>
              <Input
                name="startTime"
                id="startTime"
                type="datetime-local"
                defaultValue={now}
                required
              />
            </div>
            <div>
              <Label htmlFor="endTime">終了時間</Label>
              <Input
                name="endTime"
                id="endTime"
                type="datetime-local"
                defaultValue={end}
                required
              />
            </div>
            <div>
              <Label htmlFor="expectedMaxRps">想定最大RPS</Label>
              <Input
                id="expectedMaxRps"
                name="expectedMaxRps"
                type="number"
                required
                step="10000"
                min="10000"
                max="200000"
                defaultValue={props.scale?.Data.expectedMaxRps}
              />
            </div>
          </div>
          <DialogFooter className="pt-4">
            {props.scale && (
              <ScaleDeleteButton
                id={props.scale.ID}
                onDelete={() => props.onOpenChange(false)}
              />
            )}
            <Button type="submit" disabled={isPending}>
              保存する
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
