"use server";

import { z } from "zod";
import { v7 } from "uuid";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { deleteScale, fetchScale, putScale } from "@/adapter/dynamodb/scale";
import { setFlashMessageCookie } from "@/components/flash-message";
import { weekDays } from "@/lib/consts";
import {
  deleteScaleSchedule,
  fetchScaleSchedule,
  putScaleSchedule,
} from "@/adapter/dynamodb/scale-schedule";
import {
  createScaleBySchedule,
  executeAutoScale,
} from "@/adapter/lambda/helper-api";
import { getLoginUser } from "@/auth";
import { revalidatePath } from "next/cache";

dayjs.extend(utc);
dayjs.extend(timezone);

const localDateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/;
const CreateScaleActionSchema = z.object({
  name: z.string(),
  scaleId: z.optional(z.string()),
  programId: z.string(),
  startTime: z.string().regex(localDateTimeRegex),
  endTime: z.string().regex(localDateTimeRegex),
  expectedMaxRps: z.coerce.number().min(10000).max(200000),
});

export async function putScaleAction(
  formData: FormData,
): Promise<"ok" | "error"> {
  const data = Object.fromEntries(formData.entries());
  const parsed = CreateScaleActionSchema.parse(data);
  const mode = parsed.scaleId ? "edit" : "create";

  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(parsed.programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${parsed.programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    return "error";
  }

  const start = dayjs(parsed.startTime);
  const end = dayjs(parsed.endTime);

  if (start.isAfter(end)) {
    await setFlashMessageCookie({
      title: "開始時刻は終了時刻より前に設定してください",
      variant: "destructive",
    });
    return "error";
  }

  const diffDays = end.diff(start, "day", true);
  if (diffDays > 7) {
    await setFlashMessageCookie({
      title: "終了時刻は開始時刻より7日間以内に設定してください",
      variant: "destructive",
    });
    return "error";
  }

  // 2024-11-01T13:01 -> 2024-11-01T13:01:00+09:00
  const startTime = withTimeZone(parsed.startTime);
  const endTime = withTimeZone(parsed.endTime);

  const id = parsed.scaleId ?? v7();
  const output = await putScale(
    id,
    parsed.name,
    parsed.programId,
    startTime,
    endTime,
    parsed.expectedMaxRps,
  );

  if (output.$metadata.httpStatusCode !== 200) {
    await setFlashMessageCookie({
      title: "確定予約の保存に失敗しました",
      variant: "destructive",
    });
    return "error";
  }

  await executeAutoScale();
  await loginUser.addHistory({
    kind: mode === "create" ? "scale#create" : "scale#update",
    scaleId: id,
  });

  await setFlashMessageCookie({
    title: `確定予約を${mode === "create" ? "作成" : "更新"}しました`,
  });

  return "ok";
}

export async function deleteScaleAction(
  formData: FormData,
): Promise<"ok" | "error"> {
  const scaleId = formData.get("scaleId");
  if (typeof scaleId !== "string") {
    throw new Error(`Invalid scaleId: ${scaleId}`);
  }

  const loginUser = await getLoginUser();
  const scaleToDelete = await fetchScale(scaleId);
  if (scaleToDelete == null) {
    await setFlashMessageCookie({
      title: `スケールID ${scaleId} は存在しません`,
      variant: "destructive",
    });
    return "error";
  }
  if (!loginUser.hasAccessToProgram(scaleToDelete.Data.programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${scaleToDelete.Data.programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    return "error";
  }

  const output = await deleteScale(scaleId);
  if (output.$metadata.httpStatusCode !== 200) {
    await setFlashMessageCookie({
      title: "確定予約の削除に失敗しました",
      variant: "destructive",
    });
    return "error";
  }

  await executeAutoScale();
  await loginUser.addHistory({ kind: "scale#delete", scaleId });

  await setFlashMessageCookie({
    title: "確定予約を削除しました",
  });

  return "ok";
}

const localTimeRegex = /^\d{2}:\d{2}(:\d{2})?$/;
const CreateScaleScheduleActionSchema = z.object({
  id: z.optional(z.string()), // idがある場合は編集、ない場合は新規作成
  name: z.string(),
  programId: z.string(),
  expectedMaxRps: z.coerce.number().min(10000).max(200000),
  startTime: z.string().regex(localTimeRegex),
  endTime: z.string().regex(localTimeRegex),
  mon: z.coerce.boolean(),
  tue: z.coerce.boolean(),
  wed: z.coerce.boolean(),
  thu: z.coerce.boolean(),
  fri: z.coerce.boolean(),
  sat: z.coerce.boolean(),
  sun: z.coerce.boolean(),
});

export async function putScaleScheduleAction(
  formData: FormData,
): Promise<"ok" | "error"> {
  const data = Object.fromEntries(formData.entries());
  const { id, name, programId, expectedMaxRps, startTime, endTime, ...rest } =
    CreateScaleScheduleActionSchema.parse(data);
  const weekdays = weekDays
    .filter((day) => (rest as any)[day.value])
    .map((day) => day.value);

  if (weekdays.length === 0) {
    await setFlashMessageCookie({
      title: "曜日を1つ以上選択してください",
      variant: "destructive",
    });

    return "error";
  }

  if (startTime >= endTime) {
    await setFlashMessageCookie({
      title: "開始時刻は終了時刻より前に設定してください",
      variant: "destructive",
    });

    return "error";
  }

  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${programId} にアクセス権限がありません`,
      variant: "destructive",
    });

    return "error";
  }

  const mode = id == null ? "create" : "edit";
  const scaleScheduleId = id || v7();

  await putScaleSchedule(
    scaleScheduleId,
    name,
    programId,
    expectedMaxRps,
    startTime,
    endTime,
    weekdays,
  );

  await loginUser.addHistory({
    kind: mode === "create" ? "scale-schedule#create" : "scale-schedule#update",
    scaleScheduleId,
  });

  await setFlashMessageCookie({
    title: `繰り返し予約を${mode === "create" ? "更新" : "作成"}しました`,
  });

  return "ok";
}

export async function deleteScaleScheduleAction(
  formData: FormData,
): Promise<"ok" | "error"> {
  const id = formData.get("id");
  if (typeof id !== "string") {
    await setFlashMessageCookie({
      title: `スケジュールIDが不正です: ${id}`,
      variant: "destructive",
    });
    return "error";
  }

  const scaleSchedule = await fetchScaleSchedule(id);
  if (scaleSchedule == null) {
    await setFlashMessageCookie({
      title: `スケジュールID ${id} は存在しません`,
      variant: "destructive",
    });
    return "error";
  }

  const loginUser = await getLoginUser();
  if (!loginUser.hasAccessToProgram(scaleSchedule.Data.programId)) {
    await setFlashMessageCookie({
      title: `プログラムID ${scaleSchedule.Data.programId} にアクセス権限がありません`,
      variant: "destructive",
    });
    return "error";
  }

  await deleteScaleSchedule(id);

  await loginUser.addHistory({
    kind: "scale-schedule#delete",
    scaleScheduleId: id,
  });

  await setFlashMessageCookie({
    title: "繰り返し予約を削除しました",
  });

  return "ok";
}

function withTimeZone(t: string): string {
  return dayjs(t).tz("Asia/Tokyo").format();
}

export async function revalidateServerScalePageAction() {
  revalidatePath("/server-scale");
}

export async function createScaleByScheduleAction() {
  const loginUser = await getLoginUser();
  if (loginUser.isAdmin) {
    await createScaleBySchedule();
  }
}
