import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import Form from "next/form";
import { useActionState } from "react";
import { deleteScaleScheduleAction } from "./actions";

export function ScaleScheduleDeleteButton({
  id,
  onDelete,
}: {
  id: string;
  onDelete: () => void;
}) {
  const [error, submitAction, isPending] = useActionState<{}, FormData>(
    async (previousState, formData) => {
      const result = await deleteScaleScheduleAction(formData);
      if (result === "ok") {
        onDelete();
      }
    },
    {},
  );

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="destructive">削除する</Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <Form action={submitAction} className="space-y-4">
          <AlertDialogHeader>
            <AlertDialogTitle>繰り返し予約を削除しますか？</AlertDialogTitle>
            <AlertDialogDescription>
              この操作は取り消せません。削除してもよろしいですか？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <input type="hidden" name="id" value={id} />
          <AlertDialogFooter>
            <AlertDialogCancel>削除しない</AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button type="submit" disabled={isPending}>
                削除する
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
