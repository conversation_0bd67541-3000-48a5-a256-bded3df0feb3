import "server-only";
import { getLoginUser, LoginUser } from "@/auth";
import { UpcomingScaleTable } from "./upcoming-scale-table";
import {
  fetchProgramsByUser,
  fetchSchedulesByUser,
  fetchUpcomingScalesByPermission,
} from "./loaders";
import { Program } from "@/adapter/dynamodb/program";
import { ScaleSchedule } from "@/adapter/dynamodb/scale-schedule";
import { WeekScaleScheduleTable } from "./week-scale-table";

export async function UpcomingScales({ programId }: { programId?: string }) {
  const loginUser = await getLoginUser();
  const [programs, scales] = await Promise.all([
    fetchProgramsByUser(loginUser, programId),
    fetchUpcomingScalesByPermission(loginUser),
  ]);

  return (
    <div className="flex flex-col gap-4">
      <UpcomingScaleTable
        programs={programs}
        scales={scales}
        programId={programId}
      />
    </div>
  );
}

export async function WeekScaleSchedules({
  programId,
}: {
  programId?: string;
}) {
  const loginUser = await getLoginUser();
  const [schedules, programs] = await Promise.all([
    fetchSchedulesByUser(loginUser, programId),
    fetchProgramsByUser(loginUser, programId),
  ]);

  return (
    <WeekScaleScheduleTable
      schedules={schedules}
      programs={programs}
      programId={programId}
    />
  );
}
