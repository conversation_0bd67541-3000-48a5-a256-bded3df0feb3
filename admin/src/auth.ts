import type { NextAuthOptions, Session } from "next-auth";
import { getServerSession } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import KeycloakProvider from "next-auth/providers/keycloak";
import { environment } from "./environments";
import { fetchUser, saveUserCustomClaims, User } from "./adapter/dynamodb/user";
import { backupUserId } from "./lib/consts";
import { logger } from "./logger";
import {
  ActionType,
  addHistory as addHistoryToDynamoDB,
  addLoginHistory,
} from "./adapter/dynamodb/history";
import {
  fetchProgramPermissionsByUserId,
  ProgramPermission,
} from "./adapter/dynamodb/program-permission";
import {
  fetchPrograms as fetchProgramsFromDynamoDB,
  Program,
} from "./adapter/dynamodb/program";

export class LoginUser {
  readonly id: string;
  readonly isAdmin: boolean;
  readonly permissions: ProgramPermission[];
  readonly session: Session;

  constructor(
    session: Session,
    user: User,
    isAdmin: boolean,
    permissions: ProgramPermission[],
  ) {
    this.id = user.ID;
    this.isAdmin = isAdmin;
    this.permissions = permissions;
    this.session = session;
  }

  hasAccessToProgram(programId: string): boolean {
    if (this.isAdmin) {
      return true;
    }

    return this.permissions.some((p) => p.ID === programId);
  }

  async fetchPrograms(): Promise<Program[]> {
    const programs = await fetchProgramsFromDynamoDB();

    if (this.isAdmin) {
      return programs;
    }

    const permittedPrograms = programs.filter((program) =>
      this.permissions.some((permission) => permission.ID === program.ID),
    );

    return permittedPrograms;
  }

  isBackupUser(): boolean {
    return this.id === backupUserId;
  }

  async addHistory(action: ActionType) {
    await addHistoryToDynamoDB(this.id, action);
  }
}

export async function getLoginUser(): Promise<LoginUser> {
  const session = await getServerSession(authOptions);
  if (!session) {
    throw new Error("Not signed in");
  }

  if (!session.user?.name) {
    throw new Error("Invalid session");
  }

  const userId = session.user.name;

  if (userId === backupUserId) {
    return new LoginUser(session, { ID: backupUserId, Type: "User" }, true, []);
  }

  const [user, permissions] = await Promise.all([
    fetchUser(userId),
    fetchProgramPermissionsByUserId(userId),
  ]);
  if (!user) {
    throw new Error("User not found");
  }
  const isAdmin = user.UserType === "admin";

  return new LoginUser(session, user, isAdmin, permissions);
}

// 環境によってはKeycloakが無い
const keycloakProvider = process.env.KEYCLOAK_ID
  ? KeycloakProvider({
      clientId: process.env.KEYCLOAK_ID!,
      clientSecret: process.env.KEYCLOAK_SECRET!,
      issuer: process.env.KEYCLOAK_ISSUER,
    })
  : null;

const credentialsProvider = CredentialsProvider({
  name: "Credentials",
  credentials: {
    username: { label: "ユーザ名", type: "text" },
    password: { label: "パスワード", type: "password" },
  },
  async authorize(credentials, _req) {
    if (
      credentials?.username === backupUserId &&
      credentials.password === environment.backupPassword
    ) {
      const user = {
        id: backupUserId,
        name: backupUserId,
        email: backupUserId,
      };

      return user;
    } else {
      logger.error(credentials, "認証エラー");
      return null;
    }
  },
});

export const providers = keycloakProvider
  ? [keycloakProvider, credentialsProvider]
  : [credentialsProvider];

export const authOptions: NextAuthOptions = {
  providers,
  pages: {
    signIn: "/auth/sign-in",
  },
  session: {
    maxAge: 6 * 60 * 60, // 6時間
  },
  callbacks: {
    async signIn(params) {
      const profile = { ...params.profile };
      extractCustomClaim(profile);
      await saveUserCustomClaims(params.user.name!, profile as CustomClaims);
      await addLoginHistory(params.user.name!);
      return true;
    },
    async jwt(params) {
      const { token } = params;

      // カスタムクレームにあるユーザー情報をトークンに含める
      if (params.profile) {
        const profile = { ...params.profile };
        extractCustomClaim(profile);
        token.profile = profile;
      }
      return token;
    },
    async session(params) {
      const { session, token } = params;
      session.profile = token.profile;
      return session;
    },
  },
};

const internalUserKeys = [
  "nhkManNumber",
  "nhkName",
  "nhkNameKana",
  "nhkDepartmentCode",
  "nhkDepartmentName",
  "nhkExtensionNumber",
  "nhkDirectNumber",
  "email",
  "nhkAccountId",
] as const;

const externalUserKeys = [
  "nhkCompanyName",
  "nhkName",
  "nhkNameKana",
  "email",
  "nhkAccountId",
  "nhkDirectNumber",
] as const;

type InternalUserCustomClaims = Record<
  (typeof internalUserKeys)[number],
  string
>;
type ExternalUserCustomClaims = Record<
  (typeof externalUserKeys)[number],
  string
>;

export type CustomClaims = InternalUserCustomClaims | ExternalUserCustomClaims;

function extractCustomClaim(obj: Record<string, unknown>) {
  for (const key of Object.keys(obj)) {
    if (
      internalUserKeys.includes(key as any) ||
      externalUserKeys.includes(key as any)
    ) {
      continue;
    }

    delete obj[key];
  }
}

declare module "next-auth" {
  interface Session {
    profile?: Record<string, unknown>;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    profile?: Record<string, unknown>;
  }
}
