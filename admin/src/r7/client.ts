import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import type { components } from "./schema"; // generated by openapi-typescript
import { environment } from "@/environments";

dayjs.extend(utc);

async function fetcher<T>(url: string): Promise<T> {
  const res = await fetch(environment.r7ApiBaseURL + url);
  if (!res.ok) {
    throw new Error(
      `Failed to fetch ${url}, status: ${res.status}, message: ${await res.text()}`,
    );
  }

  return (await res.json()) as T;
}

async function fetcherOptional<T>(url: string): Promise<T | null> {
  const res = await fetch(environment.r7ApiBaseURL + url);
  if (!res.ok) {
    if (res.status === 404) {
      return null;
    }

    throw new Error(
      `Failed to fetch ${url}, status: ${res.status}, message: ${await res.text()}`,
    );
  }

  return (await res.json()) as T;
}

export async function fetchSeries(
  seriesId: string,
): Promise<components["schemas"]["TVSeries"] | null> {
  return fetcherOptional(`/t/tvseries/ts/${seriesId}.json`);
}

export async function searchSeries(
  word: string,
): Promise<components["schemas"]["Search2Response"] | null> {
  const url = new URL(environment.r7ApiBaseURL + "/s/extended2.json");
  url.searchParams.append("modeOfItem", "tv");
  url.searchParams.append("type", "series");
  url.searchParams.append("word", word);

  return fetcherOptional(url.toString());
}

export async function fetchComingSeriesBroadcasts(
  seriesId: string,
): Promise<components["schemas"]["BroadcastEventList"] | null> {
  const today = dayjs().local().format("YYYY-MM-DD");
  return fetcherOptional(
    `/f/broadcastevent/ts/${seriesId}.json?order=asc&from=${today}`,
  );
}
