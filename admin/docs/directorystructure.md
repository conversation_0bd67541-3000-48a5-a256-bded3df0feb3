# ディレクトリ構造

本プロジェクトの主なディレクトリ・ファイル構成とその役割をまとめます。

```
/（プロジェクトルート）
├── src/                # アプリケーション本体
│   ├── app/            # Next.js App Router（ページ・レイアウト・APIルート）
│   │   ├── (after-login)/   # ログイン後のページ群
│   │   ├── (before-login)/  # ログイン前のページ群
│   │   ├── api/             # APIルート
│   │   ├── layout.tsx       # 全体レイアウト
│   │   └── globals.css      # グローバルCSS
│   ├── components/    # UIコンポーネント
│   │   ├── ui/              # shadcn/uiベースの共通UI
│   │   ├── agg-api/         # 集計API関連UI
│   │   ├── server-scale/    # サーバースケール管理UI
│   │   └── ...              # その他個別コンポーネント
│   ├── adapter/       # 外部サービス連携・データアクセス層
│   │   ├── dynamodb/       # DynamoDBアクセス
│   │   ├── lambda/         # Lambda連携
│   │   ├── duckdb/         # DuckDB連携
│   │   ├── s3/             # S3連携
│   │   └── ecs/            # ECS連携
│   ├── lib/           # 共通ロジック・定数
│   ├── utils/         # 補助ユーティリティ
│   ├── hooks/         # カスタムフック
│   ├── r7/            # OpenAPIスキーマ・APIクライアント
│   ├── test/          # テスト用ユーティリティ
│   ├── auth.ts        # 認証ロジック
│   ├── environments.ts# 環境変数管理
│   ├── logger.ts      # ロギング
│   └── middleware.ts  # Next.jsミドルウェア
├── public/            # 静的ファイル（画像・PDF等）
│   └── manual/        # マニュアルPDF
├── docs/              # ドキュメント
│   ├── technologystack.md   # 技術スタックまとめ
│   └── directorystructure.md# （本ファイル）
├── .github/           # GitHub管理・CI/CD設定
├── .clinerules/       # プロジェクトルール・ガイドライン
├── package.json       # npmパッケージ管理
├── Dockerfile         # Dockerビルド定義
├── README.md          # プロジェクト概要
├── tsconfig.json      # TypeScript設定
├── next.config.ts     # Next.js設定
├── tailwind.config.ts # Tailwind CSS設定
├── vite.config.ts     # Vitest/ビルド設定
└── ...                # その他設定・隠しファイル
```

## 各ディレクトリの概要

- **src/**: アプリケーションのソースコード全体。ドメインごとに細かく分割。
- **public/**: 画像・PDF等の静的ファイル。`manual/`にマニュアルPDFを配置。
- **docs/**: 技術スタックやディレクトリ構造などのドキュメント。
- **.github/**: GitHub Actions等のCI/CDや管理用ファイル。
- **.clinerules/**: プロジェクト独自のルールやガイドライン。

## 備考

- ディレクトリやファイルは今後の機能追加・運用に応じて拡張される場合があります。
- 詳細な役割や設計方針は各ディレクトリ・ファイルのREADMEやコメントも参照してください。
