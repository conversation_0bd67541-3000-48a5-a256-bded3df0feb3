# 技術スタック

## フロントエンド

- **Next.js**（App Router、standalone出力）
- **React**
- **TypeScript**
- **Tailwind CSS**（`tailwindcss-animate`プラグイン利用、ダークモード対応）
- **Radix UI**（@radix-ui/react-xxx）
- **shadcn/ui**（`src/components/ui`配下に導入）
- **lucide-react**（アイコン）
- **clsx/class-variance-authority**（クラス名ユーティリティ）
- **date-fns / dayjs**（日付処理）
- **recharts**（グラフ描画）

## バックエンド・API

- **Next.js API Routes**（サーバーサイド処理）
- **AWS DynamoDB**（@aws-sdk/client-dynamodb, @aws-sdk/lib-dynamodb, DynamoDBDocumentClient）
- **AWS Lambda**（一部処理で連携）
- **AWS S3**（ファイル保存）
- **DuckDB**（@duckdb/node-api, @duckdb/node-bindings、分析用途）
- **OpenAPI**（swagger_nhk_r7.json から型自動生成、openapi-typescript）

## 認証・認可

- **next-auth**（認証基盤、Keycloak/Credentialsプロバイダ対応）
- **Keycloak**（環境によって利用）
- **独自認証（バックアップユーザー）**

## インフラ・デプロイ

- **Docker**（Node.js, Debian bookworm-slimベース、multi-stage build）
- **AWS（DynamoDB, Lambda, S3）**
- **AWS Lambda Adapter**（Next.jsのサーバーレス実行対応）

## 開発支援・テスト・CI

- **Vitest**（テストフレームワーク）
- **Testcontainers**（ローカルテスト用コンテナ）
- **ESLint / Prettier**（静的解析・フォーマット）
- **OpenAPI TypeScript Codegen**（API型自動生成）

## その他

- **Zod**（バリデーション）
- **Pino**（ロギング）
- **環境変数管理**（.envrc.sample, src/environments.ts）
- **Google Docs**（マニュアル管理、PDF化して配布）

---

### 設計・構成の特徴

- **App Router構成**（`src/app`配下、レイアウト/ページ/認証/サイドメニュー等を分離）
- **UIコンポーネントの共通化**（shadcn/ui, Radix UIベース）
- **AWSサービスとの連携を強く意識した設計**
- **OpenAPIスキーマによる型安全なAPI連携**
- **Dockerによる一貫した開発・本番環境構築**
