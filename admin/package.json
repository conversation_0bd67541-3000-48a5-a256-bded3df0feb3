{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "PORT=8070 next dev --turbopack", "build": "next build", "start": "next start", "test": "vitest", "test:run": "vitest run", "lint": "next lint", "format": "prettier . --write", "codegen": "openapi-typescript ./swagger_nhk_r7.json -o ./src/r7/schema.d.ts", "predev": "npm run codegen", "prebuild": "npm run codegen"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.817.0", "@aws-sdk/client-ecs": "3.817.0", "@aws-sdk/client-lambda": "3.817.0", "@aws-sdk/client-s3": "3.817.0", "@aws-sdk/credential-providers": "3.817.0", "@aws-sdk/lib-dynamodb": "3.817.0", "@duckdb/node-api": "^1.2.2-alpha.18", "@duckdb/node-bindings": "^1.2.2-alpha.18", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "lucide": "0.511.0", "lucide-react": "^0.511.0", "next": "15.3.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "pino": "^9.7.0", "react": "19.1.0", "react-day-picker": "8.10.1", "react-dom": "19.1.0", "recharts": "^2.15.2", "server-only": "^0.0.1", "sharp": "0.34.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "9.24.0", "@testcontainers/localstack": "10.28.0", "@types/node": "22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "9.24.0", "eslint-config-next": "15.3.0", "openapi-typescript": "^7.8.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "testcontainers": "10.28.0", "typescript": "^5", "vitest": "3.1.1"}}