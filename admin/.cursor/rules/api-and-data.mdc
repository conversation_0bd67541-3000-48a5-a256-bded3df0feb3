---
description: 
globs: 
alwaysApply: false
---
# API設計とデータ管理

## API構成

### R7 API
[src/r7](mdc:src/r7)ディレクトリにR7 API関連のコードが配置されています。

#### スキーマ生成
- [swagger_nhk_r7.json](mdc:swagger_nhk_r7.json) - OpenAPI仕様書
- 自動生成される型定義: `src/r7/schema.d.ts`
- 生成コマンド: `npm run codegen`

### Next.js API Routes
[src/app/api](mdc:src/app/api)ディレクトリにNext.js API Routesが配置されています。

## データアダプター層

### アダプターパターン
[src/adapter](mdc:src/adapter)ディレクトリでデータアクセス層を抽象化しています。

### データソース
- **AWS DynamoDB**: メインデータストア
- **DuckDB**: 分析用データベース
- **AWS S3**: ファイルストレージ

## 認証とセキュリティ

### NextAuth.js設定
[src/auth.ts](mdc:src/auth.ts)で認証設定を管理しています。

### ミドルウェア
[src/middleware.ts](mdc:src/middleware.ts)で認証チェックとルーティング制御を行っています。

## 環境設定

### 環境変数管理
- [.envrc.sample](mdc:.envrc.sample) - 環境変数のサンプル
- [src/environments.ts](mdc:src/environments.ts) - 環境設定の型定義

### AWS設定
以下のAWSサービスを使用：
- DynamoDB (データベース)
- S3 (ファイルストレージ)
- Lambda (サーバーレス関数)
- ECS (コンテナ実行)

## ログ管理

### ログ設定
[src/logger.ts](mdc:src/logger.ts)でPinoを使用したログ設定を管理しています。

### インストルメンテーション
[src/instrumentation.ts](mdc:src/instrumentation.ts)でアプリケーションの監視設定を行っています。

## データフロー

1. **フロントエンド** → Next.js API Routes
2. **API Routes** → データアダプター層
3. **アダプター層** → AWS DynamoDB/DuckDB
4. **レスポンス** → フロントエンドコンポーネント

## API開発ガイドライン

1. **型安全性**: OpenAPI仕様から自動生成された型を使用
2. **エラーハンドリング**: 適切なHTTPステータスコードとエラーメッセージ
3. **認証**: 必要なエンドポイントには認証チェックを実装
4. **ログ**: 重要な操作はログに記録
5. **テスト**: APIエンドポイントのテストを作成
