---
description: 
globs: 
alwaysApply: false
---
# NHKカウンター管理システム - プロジェクト概要

## プロジェクト概要
このプロジェクトは「admin」という名前のNHKカウンター管理システムです。Next.js 15とReact 19を使用したモダンなWebアプリケーションです。

## 技術スタック
- **フレームワーク**: Next.js 15 (App Router使用)
- **UI**: React 19 + TypeScript
- **スタイリング**: Tailwind CSS + shadcn/ui (Radix UI ベース)
- **認証**: NextAuth.js
- **データベース**: AWS DynamoDB + DuckDB
- **クラウド**: AWS (S3, Lambda, ECS)
- **テスト**: Vitest
- **開発ツール**: E<PERSON><PERSON>, Prettier, OpenAPI TypeScript

## 主要ディレクトリ構造
- [src/app](mdc:src/app) - Next.js App Routerのページとレイアウト
- [src/components](mdc:src/components) - 再利用可能なReactコンポーネント
- [src/lib](mdc:src/lib) - ユーティリティ関数とライブラリ
- [src/r7](mdc:src/r7) - R7 API関連のコード
- [src/adapter](mdc:src/adapter) - データアダプター層
- [public](mdc:public) - 静的ファイル
- [docs](mdc:docs) - ドキュメント

## 設定ファイル
- [package.json](mdc:package.json) - 依存関係とスクリプト
- [next.config.ts](mdc:next.config.ts) - Next.js設定
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS設定
- [tsconfig.json](mdc:tsconfig.json) - TypeScript設定
