---
description: 
globs: 
alwaysApply: false
---
# デプロイメントとDocker

## Docker設定

### Dockerfile
[Dockerfile](mdc:Dockerfile)でアプリケーションのコンテナ化を定義しています。

### Docker設定のポイント
- Next.js standaloneビルドを使用
- マルチステージビルドでイメージサイズを最適化
- [.dockerignore](mdc:.dockerignore)で不要なファイルを除外

## ビルド設定

### Next.js設定
[next.config.ts](mdc:next.config.ts)の重要な設定：
- `output: "standalone"` - Dockerデプロイメント用
- `images.unoptimized: true` - 画像最適化を無効化
- `serverExternalPackages` - DuckDBパッケージの外部化

### 本番ビルド
```bash
npm run build  # 本番用ビルド
npm start      # 本番サーバー起動
```

## 環境変数

### 必要な環境変数
[.envrc.sample](mdc:.envrc.sample)を参考に以下を設定：
- AWS認証情報
- データベース接続情報
- NextAuth.js設定
- その他のアプリケーション設定

### 環境別設定
[src/environments.ts](mdc:src/environments.ts)で環境別の設定を管理しています。

## スクリプト

### 開発用スクリプト
- `npm run dev` - 開発サーバー（ポート8070、Turbopack使用）
- `npm run test` - テスト実行
- `npm run lint` - コードチェック
- `npm run format` - コードフォーマット

### ビルド用スクリプト
- `npm run prebuild` - ビルド前のコード生成
- `npm run build` - 本番ビルド
- `npm run start` - 本番サーバー起動

### コード生成
- `npm run predev` - 開発前のコード生成
- `npm run codegen` - OpenAPI型定義生成

## デプロイメント手順

1. **環境変数設定**: `.envrc`ファイルを適切に設定
2. **依存関係インストール**: `npm ci`
3. **コード生成**: `npm run codegen`
4. **ビルド**: `npm run build`
5. **Dockerイメージ作成**: `docker build`
6. **コンテナ実行**: `docker run`

## 監視とログ

### ログ設定
[src/logger.ts](mdc:src/logger.ts)でPinoログを設定しています。

### インストルメンテーション
[src/instrumentation.ts](mdc:src/instrumentation.ts)でアプリケーション監視を設定しています。

## セキュリティ考慮事項

1. **環境変数**: 機密情報は環境変数で管理
2. **認証**: NextAuth.jsで適切な認証を実装
3. **CORS**: 必要に応じてCORS設定を調整
4. **ヘッダー**: セキュリティヘッダーの設定
