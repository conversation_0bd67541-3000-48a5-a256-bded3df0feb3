---
description: 
globs: 
alwaysApply: false
---
# コンポーネントアーキテクチャ

## UIコンポーネント構成

### shadcn/ui ベースコンポーネント
[src/components/ui](mdc:src/components/ui)ディレクトリには、shadcn/uiで生成されたベースコンポーネントが配置されています。これらは直接編集せず、shadcn/uiのCLIで管理してください。

### カスタムコンポーネント
[src/components](mdc:src/components)ディレクトリの主要コンポーネント：

#### 共通コンポーネント
- [date-picker.tsx](mdc:src/components/date-picker.tsx) - 日付選択コンポーネント
- [flash-message.tsx](mdc:src/components/flash-message.tsx) - フラッシュメッセージ表示
- [mode-toggle-button.tsx](mdc:src/components/mode-toggle-button.tsx) - ダークモード切り替え
- [submit-button.tsx](mdc:src/components/submit-button.tsx) - 送信ボタン
- [page-title.tsx](mdc:src/components/page-title.tsx) - ページタイトル

#### ナビゲーション
- [menu-link.tsx](mdc:src/components/menu-link.tsx) - メニューリンク
- [manual-dropdown.tsx](mdc:src/components/manual-dropdown.tsx) - マニュアルドロップダウン
- [signout-button.tsx](mdc:src/components/signout-button.tsx) - サインアウトボタン

#### エラーハンドリング
- [error-page.tsx](mdc:src/components/error-page.tsx) - エラーページ
- [forbidden.tsx](mdc:src/components/forbidden.tsx) - アクセス拒否ページ

#### 機能別コンポーネント
- [src/components/server-scale](mdc:src/components/server-scale) - サーバースケール関連
- [src/components/agg-api](mdc:src/components/agg-api) - 集計API関連

## レイアウト構成

### メインレイアウト
[src/app/layout.tsx](mdc:src/app/layout.tsx)がアプリケーション全体のレイアウトを定義しています。

### 認証状態別レイアウト
- [src/app/(before-login)](mdc:src/app/(before-login) - ログイン前のページ
- [src/app/(after-login)](mdc:src/app/(after-login) - ログイン後のページ

## テーマとスタイリング

### テーマプロバイダー
[src/components/theme-provider.tsx](mdc:src/components/theme-provider.tsx)でダークモード/ライトモードを管理しています。

### グローバルスタイル
[src/app/globals.css](mdc:src/app/globals.css)でアプリケーション全体のスタイルを定義しています。

## コンポーネント作成ガイドライン

1. **再利用性**: 汎用的なコンポーネントは`src/components`に配置
2. **機能別分類**: 特定機能に特化したコンポーネントはサブディレクトリに整理
3. **型安全性**: PropsにはTypeScriptの型を必ず定義
4. **アクセシビリティ**: Radix UIコンポーネントを活用してアクセシビリティを確保
5. **レスポンシブ**: Tailwind CSSのレスポンシブクラスを使用
