---
description: 
globs: 
alwaysApply: false
---
# 開発ガイドライン

## コーディング規約

### TypeScript
- 厳密な型定義を使用する
- `any`型の使用は避ける

### React/Next.js
- 関数コンポーネントを使用する
- Server ComponentsとClient Componentsを適切に使い分ける
- App Routerの規約に従う（[src/app](mdc:src/app)ディレクトリ構造）
- カスタムフックは`use`プレフィックスを付ける

### スタイリング
- Tailwind CSSを使用する
- shadcn/uiコンポーネントを活用する（[src/components/ui](mdc:src/components/ui)）
- カスタムCSSは[src/app/globals.css](mdc:src/app/globals.css)に追加する

### ファイル命名規約
- 基本的にはkebab-caseを用いる（例: `user-profile.ts`）
- ページ: Next.js App Routerの規約に従う

## 開発フロー

### 開発サーバー起動
```bash
npm run dev  # ポート8070で起動
```

### コード生成
```bash
npm run codegen  # OpenAPI仕様からTypeScript型を生成
```

### テスト実行
```bash
npm run test      # 監視モード
npm run test:run  # 一回実行
```

### コードフォーマット
```bash
npm run format  # Prettierでフォーマット
npm run lint    # ESLintでチェック
```

## 重要な設定

### 環境変数
- [.envrc.sample](mdc:.envrc.sample)を参考に`.envrc`を設定する
- AWS認証情報が必要

### API仕様
- [swagger_nhk_r7.json](mdc:swagger_nhk_r7.json)からTypeScript型を自動生成
- 生成されたファイル: [src/r7/schema.d.ts](mdc:src/r7/schema.d.ts)
