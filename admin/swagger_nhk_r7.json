{"components": {"schemas": {"Actor": {"additionalProperties": false, "description": "シリーズ、エピソードの出演者の情報", "properties": {"organization": {"$ref": "#/components/schemas/Organization"}, "person": {"$ref": "#/components/schemas/Person"}}, "type": "object"}, "AreaItem": {"additionalProperties": false, "description": "エリアに関する定数の定義です", "properties": {"areaName": {"description": "エリア名", "type": "string"}, "block": {"description": "ブロックID", "type": "string"}, "blockName": {"description": "ブロックの名称", "type": "string"}, "id": {"description": "エリアID", "type": "string"}, "name": {"description": "エリア名（重複のため削除予定）", "type": "string"}, "prefName": {"description": "都道府県名", "type": "string"}}, "required": ["id", "name", "areaName", "prefName", "block", "blockName"], "type": "object"}, "AudioMode": {"description": "音声モード", "enum": ["multiple", "ch51", "ch222", "ch51Lang2", "ch51Kaisetsu", "stereo", "kaisetsu", "lang2", "lang3", "lang4", "stereoKaisetsu", "stereoLang2", "stereoLang3", "stereoLang4"], "type": "string"}, "AudioObject": {"additionalProperties": false, "description": "オーディオオブジェクト", "properties": {"additionalProperty": {"$ref": "#/components/schemas/MediaAdditionalProperty"}, "description": {"description": "オーディオオブジェクトの説明", "type": "string"}, "detailedContent": {"description": "オーディオオブジェクトの再生情報", "items": {"$ref": "#/components/schemas/DetailedContent"}, "type": "array"}, "detailedContentStatus": {"$ref": "#/components/schemas/DetailedContentStatus"}, "duration": {"description": "オーディオブジェクトの時間長", "type": "string"}, "embedUrl": {"description": "オーディオオブジェクトの埋め込みプレイヤーのURL", "type": "string"}, "expires": {"description": "オーディオオブジェクトの公開終了日時", "format": "date-time", "type": "string"}, "id": {"description": "オーディオオブジェクト ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/AudioObjectIdentifierGroup"}, "name": {"description": "オーディオオブジェクト名", "type": "string"}, "publication": {"description": "オーディオオブジェクトのブロードキャストイベントの情報", "items": {"$ref": "#/components/schemas/BroadcastEventSummary"}, "type": "array"}, "regionsAllowed": {"description": "オーディオオブジェクトの配信地域制限 (JP:国内制限あり）", "type": "string"}, "thumbnail": {"$ref": "#/components/schemas/ImageThumbnail"}, "uploadDate": {"description": "オーディオオブジェクトの公開開始日時", "type": "string"}, "url": {"description": "オーディオオブジェクトのウェブページのURL", "type": "string"}}, "required": ["id", "identifierGroup", "detailedContentStatus"], "type": "object"}, "AudioObjectIdentifierGroup": {"additionalProperties": false, "description": "オーディオオブジェクトに関する ID をまとめた情報", "properties": {"broadcastEventId": {"description": "オーディオオブジェクトの元となるブロードキャストイベント ID", "type": "string"}, "environmentId": {"$ref": "#/components/schemas/EnvironmentId"}, "streamType": {"$ref": "#/components/schemas/StreamType"}}, "required": ["environmentId", "streamType"], "type": "object"}, "AvailableOn": {"description": "エピソードに紐付く動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter 等の情報", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer", "detailedChapter"], "type": "string"}, "AvailableOnForBroadcastEvent": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "BroadcastEvent": {"additionalProperties": false, "description": "ブロードキャストイベントの情報", "properties": {"about": {"$ref": "#/components/schemas/EpisodeSummary"}, "citation": {"description": "NHK オンデマンドなどへの関連リンク", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "description": {"description": "ブロードキャストイベントの説明（EPG200文字の番組説明）", "type": "string"}, "detailedDescription": {"$ref": "#/components/schemas/DetailedDescription"}, "duration": {"description": "ブロードキャストイベントの尺の長さ", "type": "string"}, "endDate": {"description": "ブロードキャストイベントの終了日時", "format": "date-time", "type": "string"}, "id": {"description": "ブロードキャストイベント ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/ProgramIdentifierGroup"}, "isLiveBroadcast": {"description": "配信基盤にて早戻し可能か否かのフラグ場合は true を指定", "type": "boolean"}, "location": {"$ref": "#/components/schemas/Location"}, "misc": {"$ref": "#/components/schemas/DetailedMiscellaneous", "description": "ICIS (EPG 情報登録システム) で入力された情報"}, "name": {"description": "ブロードキャストイベント名（EPG40文字の番組説明）", "type": "string"}, "posterframe": {"$ref": "#/components/schemas/ImagePosterframe"}, "posterframeList": {"description": "ブロードキャストイベントのポスターフレーム画像のリスト", "items": {"$ref": "#/components/schemas/ImagePosterframe"}, "type": "array"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}, "startDate": {"description": "ブロードキャストイベントの開始日時", "format": "date-time", "type": "string"}, "subjectOf": {"$ref": "#/components/schemas/SubjectOf"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "ブロードキャストイベントの情報を返す API の URL", "type": "string"}}, "required": ["type", "id", "name", "description", "identifierGroup", "isLiveBroadcast"], "type": "object"}, "BroadcastEventList": {"additionalProperties": false, "description": "ブロードキャストイベントのリスト情報", "properties": {"count": {"description": "ブロードキャストイベントの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "ブロードキャストイベントのリスト", "items": {"$ref": "#/components/schemas/BroadcastEvent"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "BroadcastEventListOrderBy": {"enum": ["startDate"], "type": "string"}, "BroadcastEventStatusFilter": {"enum": ["broadcasted", "scheduled"], "type": "string"}, "BroadcastEventSummary": {"additionalProperties": false, "description": "ブロードキャストイベントの概要", "properties": {"about": {"$ref": "#/components/schemas/EpisodeSummary"}, "citation": {"description": "NHK オンデマンドなどへの関連リンク", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "description": {"description": "ブロードキャストイベントの EPG の番組概要(200文字)", "type": "string"}, "detailedDescription": {"$ref": "#/components/schemas/DetailedDescription"}, "duration": {"description": "ブロードキャストイベントの尺の長さ", "type": "string"}, "endDate": {"description": "ブロードキャストイベントの終了日時", "format": "date-time", "type": "string"}, "id": {"description": "ブロードキャストイベント ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/ProgramIdentifierGroup"}, "isLiveBroadcast": {"description": "配信基盤にて早戻し可能か否かのフラグ", "type": "boolean"}, "location": {"$ref": "#/components/schemas/Location"}, "misc": {"$ref": "#/components/schemas/DetailedMiscellaneous"}, "name": {"description": "ブロードキャストイベントの名前", "type": "string"}, "posterframe": {"$ref": "#/components/schemas/ImagePosterframe"}, "posterframeList": {"description": "ブロードキャストイベントのポスターフレーム画像のリスト", "items": {"$ref": "#/components/schemas/ImagePosterframe"}, "type": "array"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}, "startDate": {"description": "ブロードキャストイベントの開始日時", "format": "date-time", "type": "string"}, "subjectOf": {"$ref": "#/components/schemas/SubjectOf"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "ブロードキャストイベントの情報を取得する API URL", "type": "string"}}, "required": ["id", "isLiveBroadcast"], "type": "object"}, "BroadcastService": {"additionalProperties": false, "description": "ブロードキャストサービスの情報", "properties": {"badge": {"$ref": "#/components/schemas/ImageBadge"}, "badge9x4": {"$ref": "#/components/schemas/ImageBadge"}, "broadcastDisplayName": {"description": "ブロードキャストサービスの表示名", "type": "string"}, "encodingFormat": {"description": "ブロードキャストサービスのエンコードフォーマット", "items": {"type": "string"}, "type": "array"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "hero": {"$ref": "#/components/schemas/ImageHero"}, "id": {"description": "bs-{サービス ID}-{エリア ID}", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/BroadcastServiceIdentifierGroup"}, "logo": {"$ref": "#/components/schemas/ImageLogo"}, "name": {"description": "ブロードキャストサービスの名前", "type": "string"}, "subjectOf": {"$ref": "#/components/schemas/SubjectOf"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "ブロードキャストサービスを取得する API の URL", "type": "string"}, "videoFormat": {"description": "ブロードキャストサービスの取り得る画像解像度のリスト", "items": {"$ref": "#/components/schemas/VideoFormat"}, "type": "array"}}, "required": ["type", "id", "name", "identifierGroup"], "type": "object"}, "BroadcastServiceIdentifierGroup": {"additionalProperties": false, "description": "ブロードキャストサービスに関連する ID をまとめた情報", "properties": {"areaId": {"description": "ブロードキャストサービスが行われるエリア ID", "type": "string"}, "areaName": {"description": "ブロードキャストサービスが行われるエリア名", "type": "string"}, "channelAreaName": {"description": "ブロードキャストサービスのチャンネルのエリア名", "type": "string"}, "channelId": {"description": "ブロードキャストサービスのチャンネル ID", "type": "string"}, "channelKey": {"description": "ブロードキャストサービスのチャンネルのキー", "type": "string"}, "channelStationName": {"description": "ブロードキャストサービスのチャンネル名", "type": "string"}, "multiChannelDisplayName": {"description": "ブロードキャストサービスのメイン/サブの名前", "type": "string"}, "serviceId": {"$ref": "#/components/schemas/ServiceId"}, "serviceName": {"description": "ブロードキャストサービス名", "type": "string"}, "shortenedDisplayName": {"description": "ブロードキャストサービスの表示の略称", "type": "string"}, "shortenedName": {"description": "ブロードキャストサービスの略称", "type": "string"}}, "required": ["serviceId", "serviceName", "shortenedName", "shortenedDisplayName"], "type": "object"}, "Citation": {"additionalProperties": false, "description": "関連リンク", "properties": {"name": {"description": "関連リンク名", "type": "string"}, "url": {"description": "関連リンクの URL", "type": "string"}}, "type": "object"}, "Const": {"additionalProperties": false, "description": "APIで利用する定数の定義です", "properties": {"area": {"description": "エリアに関する定数の定義", "items": {"$ref": "#/components/schemas/AreaItem"}, "type": "array"}, "formatGenre": {"description": "NHKで定義するフォーマットジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "genre": {"description": "EPGジャンル", "items": {"$ref": "#/components/schemas/GenreCode"}, "type": "array"}, "image": {"$ref": "#/components/schemas/ConstImage", "description": "NHKの画像"}, "service": {"description": "各サービスに関する定数の定義", "items": {"$ref": "#/components/schemas/ConstService"}, "type": "array"}, "themeGenre": {"description": "NHKで定義するテーマジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}}, "required": ["area", "service", "genre", "formatGenre", "themeGenre", "image"], "type": "object"}, "ConstImage": {"additionalProperties": false, "description": "NHKの画像です", "properties": {"ci": {"$ref": "#/components/schemas/ImageCompanyIcon", "description": "NHK画像のURL群"}}, "required": ["ci"], "type": "object"}, "ConstService": {"additionalProperties": false, "description": "各サービスに関する定数の定義です", "properties": {"badge": {"description": "バッジ画像のURL（画角2:1）", "type": "string"}, "badge9x4": {"description": "バッジ画像のURL（画角9:4）", "type": "string"}, "eyecatch": {"description": "アイキャッチ画像のURL", "type": "string"}, "hero": {"description": "ヒーロー画像のURL", "type": "string"}, "id": {"description": "サービスID", "type": "string"}, "logo": {"description": "ロゴ画像のURL", "type": "string"}, "name": {"description": "サービス名", "type": "string"}}, "required": ["id", "name", "eyecatch", "logo", "hero", "badge", "badge9x4"], "type": "object"}, "ContentStatus": {"description": "ビデオ・オーディオオブジェクトの配信状況", "enum": ["notyet", "ready", "notOffered"], "type": "string"}, "Contributor": {"additionalProperties": false, "description": "シリーズ、エピソードに出演しない関係者", "properties": {"organization": {"$ref": "#/components/schemas/Organization"}, "person": {"$ref": "#/components/schemas/Person"}}, "type": "object"}, "Copyright": {"additionalProperties": false, "description": "画像の著作権情報", "properties": {"name": {"description": "著作権者名", "type": "string"}, "notice": {"description": "著作権の表記", "type": "string"}, "type": {"$ref": "#/components/schemas/CopyrightHolderType"}, "year": {"description": "西暦", "format": "int32", "type": "integer"}}, "required": ["notice"], "type": "object"}, "CopyrightHolderType": {"description": "個人か組織の種別情報", "enum": ["Organization", "Person"], "type": "string"}, "Coverage": {"description": "放送エリアの範囲", "enum": ["local", "block", "nationwide"], "type": "string"}, "DateProgram": {"additionalProperties": false, "description": "日付指定で得られる、サービスごとの1日、または1週間の放送データです．", "properties": {"e1": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKEテレ1で放送される番組のオブジェクト"}, "e3": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKEテレ3で放送される番組のオブジェクト"}, "g1": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHK総合1で放送される番組のオブジェクト"}, "g2": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHK総合2で放送される番組のオブジェクト"}, "r1": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKラジオ第1で放送される番組のオブジェクト"}, "r2": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKラジオ第2で放送される番組のオブジェクト"}, "r3": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKFMで放送される番組のオブジェクト"}, "s1": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKBSで放送される番組のオブジェクト"}, "s2": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKBS(102ch)で放送される番組のオブジェクト"}, "s5": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKBSプレミアム4Kで放送される番組のオブジェクト"}, "s6": {"$ref": "#/components/schemas/ServiceDetailed", "description": "NHKBS8Kで放送される番組のオブジェクト"}}, "type": "object"}, "DetailedArticleBody": {"additionalProperties": false, "description": "記事の markdown 表記", "properties": {"markedBody": {"description": "Markdown 形式の本文", "type": "string"}, "markedFooter": {"description": "Markdown 形式のフッター", "type": "string"}, "markedHeader": {"description": "Markdown 形式のヘッダー", "type": "string"}}, "type": "object"}, "DetailedChapter": {"additionalProperties": false, "description": "エピソードのエピソード記事", "properties": {"articleBody": {"description": "エピソード記事の本文", "type": "string"}, "detailedArticleBody": {"$ref": "#/components/schemas/DetailedArticleBody"}, "headline": {"description": "タイトル", "type": "string"}, "image": {"$ref": "#/components/schemas/ImageThumbnail"}}, "required": ["headline", "articleBody", "detailedArticleBody"], "type": "object"}, "DetailedComingEvent": {"additionalProperties": false, "description": "エピソードの次回放送のブロードキャストイベント", "properties": {"endDate": {"description": "ブロードキャストイベントの終了日時", "format": "date-time", "type": "string"}, "id": {"description": "ブロードキャストイベント ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/ProgramIdentifierGroup"}, "misc": {"$ref": "#/components/schemas/DetailedMiscellaneous"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}, "startDate": {"description": "ブロードキャストイベントの開始日時", "format": "date-time", "type": "string"}}, "required": ["startDate"], "type": "object"}, "DetailedContent": {"additionalProperties": false, "description": "ビデオ・オーディオオブジェクトのストリーム情報", "properties": {"contentUrl": {"description": "ビデオ・オーディオオブジェクトの再生 URL", "type": "string"}, "drmCert": {"description": "ビデオ・オーディオオブジェクトの DRM の証明書 URL", "type": "string"}, "drmLicense": {"description": "ビデオ・オーディオオブジェクトの DRM のライセンスサーバ URL", "type": "string"}, "drmName": {"description": "ビデオ・オーディオオブジェクトの DRM 名", "type": "string"}, "encodingFormat": {"description": "ビデオ・オーディオオブジェクトのエンコードフォーマット", "items": {"type": "string"}, "type": "array"}, "lowContentUrl": {"description": "ビデオ・オーディオオブジェクトの低ビットレートの再生 URL", "type": "string"}, "name": {"description": "ビデオ・オーディオオブジェクトの配信名 (hls_widevine、hls_playready、hls_fairplay、hls_fmp4、dash)", "type": "string"}}, "required": ["name", "contentUrl", "encodingFormat"], "type": "object"}, "DetailedContentStatus": {"additionalProperties": false, "description": "ビデオ・オーディオオブジェクトの配信形態・配信状態", "properties": {"contentStatus": {"$ref": "#/components/schemas/ContentStatus"}, "environmentId": {"$ref": "#/components/schemas/EnvironmentId"}, "streamType": {"$ref": "#/components/schemas/StreamType"}}, "required": ["environmentId", "streamType", "contentStatus"], "type": "object"}, "DetailedDescription": {"additionalProperties": false, "description": "ブロードキャストイベントの EPG の番組概要(文字数毎)などをまとめた情報", "properties": {"epg200": {"description": "EPG の 200 文字の番組概要", "type": "string"}, "epg40": {"description": "EPG の 40 文字の番組概要", "type": "string"}, "epg80": {"description": "EPG の 80 文字の番組概要", "type": "string"}, "epgInformation": {"description": "EPG のお知らせ", "type": "string"}}, "required": ["epg40", "epg80", "epg200"], "type": "object"}, "DetailedMemo": {"additionalProperties": false, "description": "シリーズ、エピソードのメモ", "properties": {"articleBody": {"description": "シリーズのメモのプレーンテキスト", "type": "string"}, "detailedArticleBody": {"$ref": "#/components/schemas/DetailedArticleBody"}, "headline": {"description": "メモタイトル", "type": "string"}}, "required": ["headline"], "type": "object"}, "DetailedMiscellaneous": {"additionalProperties": false, "description": "ICIS・プラス配信基盤で入力された情報の詳細", "properties": {"actList": {"description": "出演者情報のリスト", "items": {"$ref": "#/components/schemas/IActor"}, "type": "array"}, "audioMode": {"description": "音声モード", "items": {"$ref": "#/components/schemas/AudioMode"}, "type": "array"}, "coverage": {"$ref": "#/components/schemas/Coverage"}, "displayAudioMode": {"description": "表示する音声モード", "items": {"$ref": "#/components/schemas/DisplayAudioMode"}, "type": "array"}, "displayVideoMode": {"$ref": "#/components/schemas/DisplayVideoMode"}, "displayVideoRange": {"$ref": "#/components/schemas/DisplayVideoRange"}, "eventShareStatus": {"$ref": "#/components/schemas/EventShareStatus"}, "freeLine": {"description": "入力されたフリー情報", "type": "string"}, "hsk": {"$ref": "#/components/schemas/DetailedMiscellaneousHSK"}, "isChangeable": {"description": "ブロードキャストイベントの変更の可能性を示す情報", "type": "boolean"}, "isInteractive": {"description": "双方向番組", "type": "boolean"}, "musicList": {"description": "音楽情報のリスト", "items": {"$ref": "#/components/schemas/IMusic"}, "type": "array"}, "playControlDVR": {"description": "早戻し配信の可否", "type": "boolean"}, "playControlMulti": {"description": "マルチ編成判定", "type": "boolean"}, "playControlSimul": {"description": "サイマル・同時配信の可否", "type": "boolean"}, "playControlVOD": {"description": "見逃し配信の可否", "type": "boolean"}, "programType": {"$ref": "#/components/schemas/ProgramType"}, "publishedPeriodFrom": {"description": "見逃し配信開始日時", "type": "string"}, "publishedPeriodTo": {"description": "見逃し配信終了日時", "type": "string"}, "releaseLevel": {"$ref": "#/components/schemas/ReleaseLevel"}, "supportCaption": {"description": "字幕補完放送", "type": "boolean"}, "supportDataBroadcast": {"description": "データ放送", "type": "boolean"}, "supportHybridcast": {"description": "ハイブリッドキャスト放送", "type": "boolean"}, "supportSign": {"description": "手話放送", "type": "boolean"}}, "type": "object"}, "DetailedMiscellaneousHSK": {"additionalProperties": false, "description": "NHK プラス配信基盤で入力された情報", "properties": {"passedDeliveryPeriod": {"description": "見逃し配信期間(秒)", "type": "string"}, "passedEndDateTime": {"description": "見逃し配信終了日時", "type": "string"}, "passedLength": {"description": "見逃し内容時間", "type": "string"}, "passedStartDateTime": {"description": "見逃し配信開始日時", "type": "string"}, "updateDateTime": {"description": "更新日時", "type": "string"}}, "type": "object"}, "DetailedRecentEvent": {"additionalProperties": false, "description": "NHK で独自に定義した、エピソードの直近のブロードキャストイベント", "properties": {"endDate": {"description": "ブロードキャストイベントの終了日時", "format": "date-time", "type": "string"}, "id": {"description": "ブロードキャストイベントの ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/ProgramIdentifierGroup"}, "misc": {"$ref": "#/components/schemas/DetailedMiscellaneous"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}, "startDate": {"description": "ブロードキャストイベントの開始日時", "format": "date-time", "type": "string"}}, "required": ["startDate"], "type": "object"}, "DetailedTimetable": {"additionalProperties": false, "description": "シリーズのタイムテーブル", "properties": {"description": {"description": "タイムテーブルの説明", "type": "string"}, "isRerun": {"description": "再放送フラグ", "type": "boolean"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}}, "required": ["isRerun", "description"], "type": "object"}, "DisplayAudioMode": {"description": "表示する音声モード", "enum": ["multiple", "ch51", "ch222", "stereo", "kaisetsu", "lang2", "lang3", "lang4"], "type": "string"}, "DisplayVideoMode": {"description": "映像の解像度", "enum": ["4K", "2K", "none"], "type": "string"}, "DisplayVideoRange": {"description": "映像のダイナミックレンジ", "enum": ["none", "sdr", "hdr"], "type": "string"}, "EnvironmentId": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "EpisodeAdditionalProperty": {"additionalProperties": false, "description": "NHK で定義したエピソードのプロパティをまとめた情報", "properties": {}, "type": "object"}, "EpisodeDateFilterBy": {"description": "エピソードを並び替えるパラメータ", "enum": ["dateModified", "releasedEvent", "recentEvent", "comingEvent"], "type": "string"}, "EpisodeIdentifierGroup": {"additionalProperties": false, "description": "エピソードに関連する ID をまとめた情報", "properties": {"aliasId": {"description": "エピソードのシリーズの別名", "type": "string"}, "formatGenreTag": {"description": "エピソードのフォーマットジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "hashtag": {"description": "エピソードのハッシュタグのリスト", "items": {"type": "string"}, "type": "array"}, "packedRadioSeriesName": {"description": "パックされたラジオシリーズ名", "type": "string"}, "packedTVSeriesName": {"description": "パックされたテレビシリーズ名", "type": "string"}, "radioEpisodeId": {"description": "ラジオエピソードID", "type": "string"}, "radioEpisodeName": {"description": "ラジオエピソード名", "type": "string"}, "radioSeriesId": {"description": "ラジオシリーズまたはラジオシリーズパックID", "type": "string"}, "radioSeriesName": {"description": "ラジオシリーズ名", "type": "string"}, "radioSeriesNameOriginal": {"description": "ラジオシリーズ名のオリジナル", "type": "string"}, "serviceId": {"description": "エピソードのブロードキャストサービスのリスト", "items": {"$ref": "#/components/schemas/ServiceId"}, "type": "array"}, "siteId": {"description": "サイトID", "type": "string"}, "themeGenreTag": {"description": "エピソードのテーマジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "tvEpisodeId": {"description": "テレビエピソードID", "type": "string"}, "tvEpisodeName": {"description": "テレビエピソード名", "type": "string"}, "tvSeriesId": {"description": "テレビシリーズまたはテレビシリーズパックID", "type": "string"}, "tvSeriesName": {"description": "テレビシリーズ名", "type": "string"}, "tvSeriesNameOriginal": {"description": "テレビシリーズ名のオリジナル", "type": "string"}}, "type": "object"}, "EpisodeListOrderBy": {"enum": ["episodeNumber", "releasedEvent", "recentEvent", "comingEvent", "dateModified"], "type": "string"}, "EpisodeOrderBy": {"description": "CMS1 にてシリーズごとに設定されるデフォルトの「API のエピソードの並び順」", "enum": ["episodeNumber", "releasedEvent", "recentEvent", "comingEvent"], "type": "string"}, "EpisodeOrderByForSearch": {"default": "score", "description": "検索結果のタイプを並び替えるパラメータ", "enum": ["score", "dateModified", "releasedEvent", "recentEvent", "comingEvent", "episodeNumber"], "type": "string"}, "EpisodeStatusFilter": {"description": "放送ステータス（未放送または放送済）", "enum": ["broadcasted", "scheduled"], "type": "string"}, "EpisodeSummary": {"additionalProperties": false, "description": "エピソードの概要情報", "properties": {"additionalProperty": {"$ref": "#/components/schemas/EpisodeAdditionalProperty"}, "audio": {"description": "エピソードの音声情報", "items": {"$ref": "#/components/schemas/AudioObject"}, "type": "array"}, "canonical": {"description": "エピソードのウェブページの URL", "type": "string"}, "description": {"description": "エピソードの説明", "type": "string"}, "detailedEpisodeNameRuby": {"description": "エピソード名のよみがな", "type": "string"}, "episodeNumber": {"description": "エピソードの話数", "format": "int32", "type": "integer"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "eyecatchList": {"description": "エピソードのアイキャッチズ画像情報のリスト", "items": {"$ref": "#/components/schemas/ImageEyecatch"}, "type": "array"}, "id": {"description": "エピソード ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/EpisodeIdentifierGroup"}, "keyword": {"description": "エピソードのキーワード", "items": {"type": "string"}, "type": "array"}, "name": {"description": "エピソード名", "type": "string"}, "partOfSeries": {"$ref": "#/components/schemas/SeriesSummary"}, "url": {"description": "エピソードの情報を返す API の URL", "type": "string"}, "video": {"description": "エピソードの本編動画情報", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}}, "required": ["id", "name", "identifierGroup", "keyword", "description", "url", "additionalProperty"], "type": "object"}, "EventShareStatus": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "EventShareStatusForCut": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "GenreCode": {"additionalProperties": false, "description": "EPG のジャンルコード", "properties": {"id": {"description": "genreId", "type": "string"}, "name1": {"description": "ジャンル大分類", "type": "string"}, "name2": {"description": "ジャンル中分類", "type": "string"}}, "required": ["id"], "type": "object"}, "GenreItem": {"additionalProperties": false, "description": "テーマジャンル、フォーマットジャンルの情報", "properties": {"id": {"description": "テーマジャンルまたはフォーマットジャンル ID", "type": "string"}, "name": {"description": "ジャンル名", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "IActor": {"additionalProperties": false, "description": "ICIS で登録されたブロードキャストイベントの出演者情報", "properties": {"name": {"description": "出演者名", "type": "string"}, "nameRuby": {"description": "よみがな", "type": "string"}, "role": {"description": "役割", "type": "string"}, "title": {"description": "肩書/役名", "type": "string"}}, "required": ["name"], "type": "object"}, "IArtist": {"additionalProperties": false, "description": "ICIS で登録されたブロードキャストイベントの演奏者/歌唱者の情報", "properties": {"name": {"description": "名前", "type": "string"}, "part": {"description": "演奏・歌唱・パート", "type": "string"}, "role": {"description": "役名（オペラなど）", "type": "string"}}, "type": "object"}, "IMusic": {"additionalProperties": false, "description": "ICIS で登録されたブロードキャストイベントの楽曲情報", "properties": {"arranger": {"description": "編曲家", "type": "string"}, "byArtist": {"description": "アーティスト", "items": {"$ref": "#/components/schemas/IArtist"}, "type": "array"}, "code": {"description": "品番、型番", "type": "string"}, "composer": {"description": "作曲家", "type": "string"}, "duration": {"description": "演奏時間", "type": "string"}, "label": {"description": "レーベル", "type": "string"}, "location": {"description": "収録場所", "type": "string"}, "lyricist": {"description": "作詞家", "type": "string"}, "name": {"description": "楽曲名", "type": "string"}, "nameruby": {"description": "楽曲名よみがな", "type": "string"}, "provider": {"description": "サービスプロバイダー", "type": "string"}}, "type": "object"}, "ImageBadge": {"additionalProperties": false, "description": "2:1 または 9:4 の横型画像", "properties": {"main": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageClassic": {"additionalProperties": false, "description": "4:3 の横型画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageCompanyIcon": {"additionalProperties": false, "description": "NHK ロゴ画像", "properties": {"eyecatch": {"description": "NHK アイキャッチ画像 URL", "type": "string"}, "hero": {"description": "NHK ヒーロー画像 URL", "type": "string"}, "logo": {"description": "NHK ロゴ画像 URL", "type": "string"}}, "required": ["eyecatch", "logo", "hero"], "type": "object"}, "ImageEyecatch": {"additionalProperties": false, "description": "16:9 の横型画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "large": {"$ref": "#/components/schemas/ImageObject"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageHero": {"additionalProperties": false, "description": "3:1 の横型画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageLogo": {"additionalProperties": false, "description": "1:1 の画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageObject": {"additionalProperties": false, "description": "画像情報", "properties": {"height": {"description": "画像の高さ", "type": "integer"}, "url": {"description": "画像のURL", "type": "string"}, "width": {"description": "画像の幅", "type": "integer"}}, "type": "object"}, "ImageObjectForSprite": {"additionalProperties": false, "description": "スプライト画像のオブジェクト", "properties": {"height": {"description": "画像の高さ", "type": "integer"}, "index": {"description": "[開始秒数, 動画時間 - タイル間の秒数]", "items": {"type": "integer"}, "type": "array"}, "tileDuration": {"description": "タイル間の秒数", "type": "integer"}, "tileHeight": {"description": "タイル縦幅", "type": "integer"}, "tileHeightSize": {"description": "タイル縦数", "type": "integer"}, "tileWidth": {"description": "タイル横幅", "type": "integer"}, "tileWidthSize": {"description": "タイル横数", "type": "integer"}, "url": {"description": "画像 URL", "type": "string"}, "width": {"description": "画像の幅", "type": "integer"}}, "required": ["url", "width", "height", "index", "tileWidth", "tileHeight", "tileWidthSize", "tileHeightSize", "tileDuration"], "type": "object"}, "ImagePanorama": {"additionalProperties": false, "description": "9:5 の横画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImagePosterframe": {"additionalProperties": false, "description": "16:9 の横型画像", "properties": {"main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageRole": {"additionalProperties": false, "description": "人物画像（1:1 の画像）", "properties": {"copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageSprite": {"additionalProperties": false, "description": "ビデオオブジェクトのスプライト画像", "properties": {"main": {"$ref": "#/components/schemas/ImageObjectForSprite"}, "medium": {"$ref": "#/components/schemas/ImageObjectForSprite"}}, "type": "object"}, "ImageThumbnail": {"additionalProperties": false, "description": "16:9 の横型画像", "properties": {"main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageVertical": {"additionalProperties": false, "description": "3:4 の縦型画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "ImageVertical2x3": {"additionalProperties": false, "description": "2:3 の縦型画像", "properties": {"caption": {"description": "キャプション", "type": "string"}, "copyright": {"$ref": "#/components/schemas/Copyright"}, "main": {"$ref": "#/components/schemas/ImageObject"}, "medium": {"$ref": "#/components/schemas/ImageObject"}, "small": {"$ref": "#/components/schemas/ImageObject"}}, "type": "object"}, "LayoutPattern": {"description": "コンポーネントのレイアウトの種類", "enum": ["summary", "featuredItem", "itemList", "largeImage"], "type": "string"}, "Location": {"additionalProperties": false, "description": "ブロードキャストイベントの発信局", "properties": {"id": {"description": "発信局の識別子", "type": "string"}, "name": {"description": "地域名", "type": "string"}}, "required": ["name"], "type": "object"}, "MediaAdditionalProperty": {"additionalProperties": false, "description": "NHK で独自に定義したビデオオブジェクトのプロパティをまとめた情報", "properties": {"typeOfUpload": {"description": "アップロードされた動画の種類", "type": "string"}}, "required": ["typeOfUpload"], "type": "object"}, "NDeck": {"additionalProperties": false, "description": "デッキの情報", "properties": {"additionalProperty": {"$ref": "#/components/schemas/NDeckAdditionalProperty"}, "dateModified": {"description": "デッキのいずれかのプロパティが更新された日時", "format": "date-time", "type": "string"}, "description": {"description": "デッキの説明", "type": "string"}, "id": {"description": "デッキ ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/NDeckIdentifierGroup"}, "name": {"description": "デッキ名", "type": "string"}, "playlistUrl": {"description": "デッキ下のプレイリストのリストを取得する API の URL", "type": "string"}, "sameAs": {"description": "デッキと同一内容のリンクの URL", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "デッキ情報を取得する API の URL", "type": "string"}}, "required": ["type", "id", "name", "identifierGroup", "additionalProperty", "url", "playlistUrl"], "type": "object"}, "NDeckAdditionalProperty": {"additionalProperties": false, "description": "NHK で定義したデッキのプロパティをまとめた情報", "properties": {"modeOfItem": {"description": "デッキ下のプレイリストに編成されるアイテムのモード (tv、radio など)", "type": "string"}, "typeOfDeck": {"description": "デッキの種別", "type": "string"}, "typeOfItem": {"description": "デッキ下のプレイリストに編成されるアイテムのタイプ (TVEpisode など)", "type": "string"}}, "required": ["typeOfDeck", "modeOfItem", "typeOfItem"], "type": "object"}, "NDeckIdentifierGroup": {"additionalProperties": false, "description": "デッキに関連する各種 ID をまとめた情報", "properties": {"deckId": {"description": "デッキ ID", "type": "string"}, "deckName": {"description": "デッキ名", "type": "string"}, "deckUId": {"description": "デッキ UUID", "type": "string"}}, "required": ["deckId", "deckName"], "type": "object"}, "NPlaylist": {"additionalProperties": false, "description": "プレイリストの情報", "properties": {"actor": {"description": "このプレイリストで特定のロールを演じる出演者(ドラマの出演者など)", "items": {"$ref": "#/components/schemas/Actor"}, "type": "array"}, "additionalProperty": {"$ref": "#/components/schemas/NPlaylistAdditionalProperty"}, "associatedItemUrl": {"description": "プレイリストに関連するアイテムの情報を返す API の URL", "type": "string"}, "associatedMediaUrl": {"description": "新標準の「動画」タブ内に入る動画のURL(series由来)", "items": {"type": "string"}, "type": "array"}, "canonical": {"description": "プレイリストのウェブページの URL", "type": "string"}, "citation": {"description": "このプレイリストと関連するリンク", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "classic": {"$ref": "#/components/schemas/ImageClassic"}, "contributor": {"description": "このプレイリストの出演者・関係者(MC、ゲスト、キャスター、ディレクターなど)", "items": {"$ref": "#/components/schemas/Contributor"}, "type": "array"}, "dateCreated": {"description": "このプレイリストの作成時刻", "format": "date-time", "type": "string"}, "dateModified": {"description": "このプレイリストのいずれかのプロパティが修正された時刻", "format": "date-time", "type": "string"}, "datePublished": {"description": "このプレイリストの最初の放送日", "format": "date-time", "type": "string"}, "description": {"description": "このプレイリストを説明するテキスト", "type": "string"}, "detailedCatch": {"description": "このプレイリストのキャッチコピー", "type": "string"}, "detailedMemo": {"description": "このプレイリストのメモ", "items": {"$ref": "#/components/schemas/DetailedMemo"}, "type": "array"}, "detailedPlaylistNameRuby": {"description": "プレイリストの名前のよみがな", "type": "string"}, "detailedShortName": {"description": "", "type": "string"}, "detailedTimetable": {"description": "このプレイリストのタイムテーブル", "items": {"$ref": "#/components/schemas/DetailedTimetable"}, "type": "array"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "hero": {"$ref": "#/components/schemas/ImageHero"}, "id": {"description": "プレイリスト ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/NPlaylistIdentifierGroup"}, "itemUrl": {"description": "プレイリスト内のアイテムの情報を返す API の URL", "type": "string"}, "keyword": {"description": "このプレイリストを表すキーワード", "items": {"type": "string"}, "type": "array"}, "logo": {"$ref": "#/components/schemas/ImageLogo"}, "name": {"description": "プレイリストの名前", "type": "string"}, "panorama": {"$ref": "#/components/schemas/ImagePanorama"}, "playlisticle": {"$ref": "#/components/schemas/Playlisticle", "description": "プレイリストの記事"}, "relatedDeck": {"description": "関連するデッキ", "items": {"$ref": "#/components/schemas/NDeck"}, "type": "array"}, "releasedEvent": {"$ref": "#/components/schemas/PublicationEvent"}, "sameAs": {"description": "このプレイリストと同一内容のリンク", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "style": {"$ref": "#/components/schemas/Style"}, "type": {"description": "（固定）NPlaylist", "type": "string"}, "url": {"description": "プレイリストの情報を返す API の URL", "type": "string"}, "vertical": {"$ref": "#/components/schemas/ImageVertical"}, "vertical2x3": {"$ref": "#/components/schemas/ImageVertical2x3"}}, "required": ["type", "id", "name", "identifierGroup", "additionalProperty", "relatedDeck", "url", "itemUrl", "actor", "contributor"], "type": "object"}, "NPlaylistAdditionalProperty": {"additionalProperties": false, "description": "NHK で定義したプレイリストのプロパティをまとめた情報", "properties": {"availableOnPlus": {"description": "配信タブON/OFF", "type": "boolean"}, "enableVariablePlayBackSpeedControl": {"description": "ビデオオブジェクトが可変の速度で再生できるかを示すフラグ", "type": "boolean"}, "episodeOrderBy": {"$ref": "#/components/schemas/EpisodeOrderBy"}, "layoutPattern": {"$ref": "#/components/schemas/LayoutPattern"}, "modeOfItem": {"description": "アイテムのモード(とりうる値:tv/radio)", "type": "string"}, "optional": {"description": "表示上ミュートしてもよい取扱いのもの[releasedEvent]", "items": {"type": "string"}, "type": "array"}, "publishLevel": {"$ref": "#/components/schemas/PublishLevel"}, "seriesPackStatus": {"$ref": "#/components/schemas/SeriesPackStatus"}, "typeOfEditorial": {"description": "EditorialHandsで作成されたのプレイリストのタイプ [sameTheme、sameSeries、other]", "type": "string"}, "typeOfItem": {"description": "アイテムのタイプ ex)tvEpisode、radioEpisode、", "type": "string"}, "typeOfList": {"$ref": "#/components/schemas/TypeOfList", "description": "プレイリストの型"}}, "required": ["layoutPattern", "typeOfList", "modeOfItem", "typeOfItem"], "type": "object"}, "NPlaylistIdentifierGroup": {"additionalProperties": false, "description": "プレイリストに関連するIDをまとめた情報", "properties": {"aliasId": {"description": "別名ID", "type": "string"}, "formatGenre": {"description": "NHKで定義するフォーマットジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "hashtag": {"description": "ハッシュタグ", "items": {"type": "string"}, "type": "array"}, "playlistId": {"description": "プレイリストID", "type": "string"}, "playlistName": {"description": "プレイリスト名", "type": "string"}, "playlistUId": {"description": "検索のプレイリストの UUID", "type": "string"}, "radioSeriesId": {"description": "ラジオシリーズID", "type": "string"}, "serviceId": {"description": "ブロードキャストサービスの識別子", "items": {"$ref": "#/components/schemas/ServiceId"}, "type": "array"}, "themeGenre": {"description": "NHKで定義するテーマジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "tvSeriesId": {"description": "テレビシリーズID", "type": "string"}}, "required": ["playlistId", "playlistName"], "type": "object"}, "NPlaylistList": {"additionalProperties": false, "description": "プレイリストのリスト", "properties": {"count": {"description": "プレイリストの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "プレイリスト情報群", "items": {"$ref": "#/components/schemas/NPlaylist"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "NowProgram": {"additionalProperties": false, "description": "現在と、その前後のブロードキャストイベントのリストです．", "properties": {"e1": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "e1で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "e3": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "e3で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "g1": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "g1で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "g2": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "g2で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "r1": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "r1で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "r2": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "r2で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "r3": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "r3で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "s1": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "s1で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "s2": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "s2で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "s5": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "s5で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}, "s6": {"$ref": "#/components/schemas/ServiceDetailedForNow", "description": "s6で放送される現在直前直後の3番組の放送日時、場所、サービスなどを表すオブジェクト"}}, "type": "object"}, "Organization": {"additionalProperties": false, "description": "シリーズ、エピソードに出演、関係するグループの情報", "properties": {"characterName": {"description": "役名（出演ありかつ演じる役がある場合）", "type": "string"}, "description": {"description": "出演ありの場合:番組内での役割出演なしの場合:説明", "type": "string"}, "detailedCharacterNameRuby": {"description": "役名よみがな（出演ありかつ演じる役がある場合）", "type": "string"}, "detailedNameDescription": {"description": "名前の注釈", "type": "string"}, "detailedNameRuby": {"description": "よみがな", "type": "string"}, "displayRoleName": {"description": "番組での役割の表示", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/OrganizationIdentifierGroup"}, "image": {"$ref": "#/components/schemas/ImageRole"}, "memberOf": {"description": "所属・グループなど", "items": {"type": "string"}, "type": "array"}, "name": {"description": "グループ・団体名", "type": "string"}, "roleName": {"description": "番組での役割", "type": "string"}, "subjectOf": {"description": "制作・作品", "items": {"type": "string"}, "type": "array"}}, "required": ["<PERSON><PERSON><PERSON>", "identifierGroup"], "type": "object"}, "OrganizationIdentifierGroup": {"additionalProperties": false, "description": "シリーズ、エピソードに出演、関係するグループの ID をまとめた情報", "properties": {"npgId": {"description": "NHKが付与した人物ID", "type": "string"}, "viafId": {"description": "バーチャル国際典拠ファイル（VIAF）の識別子", "type": "string"}, "wikidataId": {"description": "ウィキメディア財団が提供する共同編集型のデータベース（ウィキデータ）の識別子", "type": "string"}}, "required": ["npgId"], "type": "object"}, "PFProgram": {"additionalProperties": false, "description": "現在と次のブロードキャストイベントの情報を返します", "properties": {"e1": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "e1で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "e3": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "e3で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "g1": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "g1で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "g2": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "g2で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "r1": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "r1で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "r2": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "r2で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "r3": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "r3で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "s1": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "s1で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "s2": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "s2で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "s5": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "s5で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}, "s6": {"$ref": "#/components/schemas/ServiceDetailedForPF", "description": "s6で放送される現在と次のブロードキャストイベントの放送日時、場所、サービスなどを表すオブジェクト"}}, "type": "object"}, "Person": {"additionalProperties": false, "description": "シリーズ、エピソードの出演者、関係者の情報", "properties": {"additionalName": {"description": "ミドルネーム", "type": "string"}, "characterName": {"description": "役名(出演ありかつ演じる役がある場合)", "type": "string"}, "description": {"description": "出演ありの場合は番組での役割の説明、出演なしの場合は説明", "type": "string"}, "detailedAdditionalNameRuby": {"description": "ミドルネームよみがな", "type": "string"}, "detailedCharacterNameRuby": {"description": "役名よみがな (出演ありかつ演じる役がある場合)", "type": "string"}, "detailedFamilyNameRuby": {"description": "姓よみがな", "type": "string"}, "detailedGivenNameRuby": {"description": "名よみがな", "type": "string"}, "detailedNameDescription": {"description": "名前の注釈", "type": "string"}, "detailedNameRuby": {"description": "表記名よみがな", "type": "string"}, "displayRoleName": {"description": "番組での役割の表示名", "type": "string"}, "familyName": {"description": "姓", "type": "string"}, "givenName": {"description": "名", "type": "string"}, "hasOccupation": {"description": "職業", "items": {"type": "string"}, "type": "array"}, "honorificSuffix": {"description": "敬称", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/PersonIdentifierGroup"}, "image": {"$ref": "#/components/schemas/ImageRole"}, "jobTitle": {"description": "肩書き", "items": {"type": "string"}, "type": "array"}, "memberOf": {"description": "所属・グループなど", "items": {"type": "string"}, "type": "array"}, "name": {"description": "表記名", "type": "string"}, "roleName": {"description": "番組での役割", "type": "string"}, "subjectOf": {"description": "制作作品", "items": {"type": "string"}, "type": "array"}}, "required": ["<PERSON><PERSON><PERSON>", "identifierGroup"], "type": "object"}, "PersonIdentifierGroup": {"additionalProperties": false, "description": "シリーズ、エピソードに出演、関係する人物の ID をまとめた情報", "properties": {"npgId": {"description": "NHKが付与した人物ID", "type": "string"}, "viafId": {"description": "バーチャル国際典拠ファイル（VIAF）の識別子", "type": "string"}, "wikidataId": {"description": "ウィキメディア財団が提供する共同編集型のデータベース（ウィキデータ）の識別子", "type": "string"}}, "required": ["npgId"], "type": "object"}, "Playlisticle": {"additionalProperties": false, "description": "プレイリスト記事", "properties": {"articleBody": {"description": "本文のプレーンテキスト", "type": "string"}, "author": {"description": "著者名", "type": "string"}, "authorType": {"description": "著者のタイプ (個人、グループ)", "type": "string"}, "dateCreated": {"description": "作成日時", "format": "date-time", "type": "string"}, "dateModified": {"description": "いずれかの項目が更新された日時", "format": "date-time", "type": "string"}, "datePublished": {"description": "公開された日時", "format": "date-time", "type": "string"}, "detailedArticleBody": {"$ref": "#/components/schemas/DetailedArticleBody"}, "headline": {"description": "見出し", "type": "string"}, "publisher": {"description": "発行者名", "type": "string"}, "publisherType": {"description": "発行者のタイプ (個人、グループ)", "type": "string"}}, "type": "object"}, "ProgramIdentifierGroup": {"additionalProperties": false, "description": "ブロードキャストイベントに関連する各種 ID のまとめ", "properties": {"areaId": {"description": "ブロードキャストイベントの放送エリアの識別子", "type": "string"}, "broadcastEventId": {"description": "ブロードキャストイベントの識別子", "type": "string"}, "date": {"description": "ブロードキャストイベントの編成日", "format": "date", "type": "string"}, "eventId": {"description": "ブロードキャストイベントの数字 5 桁のイベント ID", "type": "string"}, "genre": {"description": "ブロードキャストイベントの EPG ジャンル", "items": {"$ref": "#/components/schemas/GenreCode"}, "type": "array"}, "onid": {"description": "オリジナルネットワーク ID (範囲は[0..0xffff])", "type": "string"}, "radioEpisodeId": {"description": "ラジオエピソード ID", "type": "string"}, "radioEpisodeName": {"description": "ラジオエピソード名", "type": "string"}, "radioSeriesId": {"description": "ラジオシリーズ ID", "type": "string"}, "radioSeriesName": {"description": "ラジオシリーズ名", "type": "string"}, "serviceId": {"$ref": "#/components/schemas/ServiceId"}, "sid": {"description": "サービス ID (範囲は[0..0xffff])", "type": "string"}, "siteId": {"description": "番組サイトID", "type": "string"}, "stationId": {"description": "ブロードキャストイベントの放送局の識別子", "type": "string"}, "systemUniqueId": {"description": "システムユニークID", "type": "string"}, "tsid": {"description": "トランスポートストリーム ID (範囲は[0..0xffff])", "type": "string"}, "tvEpisodeId": {"description": "テレビエピソード ID", "type": "string"}, "tvEpisodeName": {"description": "テレビエピソード名", "type": "string"}, "tvSeriesId": {"description": "テレビシリーズ ID", "type": "string"}, "tvSeriesName": {"description": "テレビシリーズ名", "type": "string"}}, "required": ["stationId", "date", "eventId", "genre"], "type": "object"}, "ProgramType": {"description": "ブロードキャストイベントの番組の種類", "enum": ["program", "spot", "other"], "type": "string"}, "PublicationEvent": {"additionalProperties": false, "description": "パブリケーションイベント (丸め前) 情報", "properties": {"displayStartDate": {"description": "表示用パブリケーションイベントの開始日時", "type": "string"}, "endDate": {"description": "パブリケーションイベントの終了日時", "format": "date-time", "type": "string"}, "id": {"description": "パブリケーションイベントの ID", "type": "string"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService"}, "startDate": {"description": "パブリケーションイベントの開始日時", "format": "date-time", "type": "string"}}, "type": "object"}, "PublishLevel": {"description": "CMS で設定されるシリーズ・プレイリストの公開レベル", "enum": ["notyet", "ready", "full", "limited", "gone"], "type": "string"}, "RadioEpisode": {"additionalProperties": false, "description": "ラジオエピソードの情報", "properties": {"actor": {"description": "エピソードで出演ありの出演者", "items": {"$ref": "#/components/schemas/Actor"}, "type": "array"}, "additionalProperty": {"$ref": "#/components/schemas/EpisodeAdditionalProperty"}, "associatedMedia": {"description": "エピソードの関連動画情報", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}, "audio": {"description": "エピソードのオーディオオブジェクト情報", "items": {"$ref": "#/components/schemas/AudioObject"}, "type": "array"}, "canonical": {"description": "エピソードのウェブページの URL", "type": "string"}, "citation": {"description": "エピソードと関連するウェブページの URL", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "classic": {"$ref": "#/components/schemas/ImageClassic"}, "contributor": {"description": "エピソードで出演なしの関係者", "items": {"$ref": "#/components/schemas/Contributor"}, "type": "array"}, "dateCreated": {"description": "エピソードが生成された日時", "format": "date-time", "type": "string"}, "dateModified": {"description": "エピソードの CMS1 での更新日時", "format": "date-time", "type": "string"}, "description": {"description": "エピソードの説明", "type": "string"}, "detailedChapter": {"description": "エピソードのエピソード記事", "items": {"$ref": "#/components/schemas/DetailedChapter"}, "type": "array"}, "detailedComingEvent": {"$ref": "#/components/schemas/DetailedComingEvent"}, "detailedEpisodeNameRuby": {"description": "エピソード名のよみがな", "type": "string"}, "detailedMemo": {"description": "エピソードのメモ", "items": {"$ref": "#/components/schemas/DetailedMemo"}, "type": "array"}, "detailedRecentEvent": {"$ref": "#/components/schemas/DetailedRecentEvent"}, "episodeNumber": {"description": "エピソードの話数", "format": "int32", "type": "integer"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "eyecatchList": {"description": "エピソードのアイキャッチズ画像情報のリスト", "items": {"$ref": "#/components/schemas/ImageEyecatch"}, "type": "array"}, "id": {"description": "エピソード名", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/EpisodeIdentifierGroup"}, "keyword": {"description": "エピソードのキーワード", "items": {"type": "string"}, "type": "array"}, "name": {"description": "エピソードの名前", "type": "string"}, "partOfSeries": {"$ref": "#/components/schemas/SeriesSummary"}, "releasedEvent": {"$ref": "#/components/schemas/PublicationEvent"}, "sameAs": {"description": "エピソードと同一内容の URL", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "エピソードの情報を返す API の URL", "type": "string"}, "vertical2x3": {"$ref": "#/components/schemas/ImageVertical2x3"}, "video": {"description": "エピソードの本編動画情報", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}}, "required": ["type", "id", "name", "identifierGroup", "keyword", "description", "actor", "contributor", "additionalProperty"], "type": "object"}, "RadioEpisodeList": {"additionalProperties": false, "description": "ラジオエピソードリスト情報", "properties": {"count": {"description": "ラジオエピソードの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "ラジオエピソードリスト", "items": {"$ref": "#/components/schemas/RadioEpisode"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "RadioSeries": {"additionalProperties": false, "description": "ラジオシリーズ（パック）の情報", "properties": {"actor": {"description": "シリーズ（パック）で出演ありの出演者", "items": {"$ref": "#/components/schemas/Actor"}, "type": "array"}, "additionalProperty": {"$ref": "#/components/schemas/SeriesAdditionalProperty"}, "associatedMediaUrl": {"description": "シリーズに関連する動画情報", "items": {"type": "string"}, "type": "array"}, "canonical": {"description": "シリーズ（パック）のウェブページの URL", "type": "string"}, "citation": {"description": "シリーズ（パック）の関連リンク", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "classic": {"$ref": "#/components/schemas/ImageClassic"}, "contributor": {"description": "シリーズ（パック）で出演なしの関係者", "items": {"$ref": "#/components/schemas/Contributor"}, "type": "array"}, "dateCreated": {"description": "シリーズ（パック）の作成日時", "format": "date-time", "type": "string"}, "dateModified": {"description": "シリーズ（パック）のいずれかプロパティの更新日時", "format": "date-time", "type": "string"}, "description": {"description": "シリーズ（パック）の説明", "type": "string"}, "detailedCatch": {"description": "シリーズ（パック）のキャッチコピー", "type": "string"}, "detailedMemo": {"description": "シリーズ（パック）のメモのリスト", "items": {"$ref": "#/components/schemas/DetailedMemo"}, "type": "array"}, "detailedSeriesNameRuby": {"description": "シリーズ（パック）名のよみがな", "type": "string"}, "detailedTimetable": {"description": "シリーズ（パック）のタイムテーブル", "items": {"$ref": "#/components/schemas/DetailedTimetable"}, "type": "array"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "hero": {"$ref": "#/components/schemas/ImageHero"}, "id": {"description": "シリーズ（パック）ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/SeriesIdentifierGroup"}, "itemUrl": {"description": "シリーズ（パック）のエピソードを取得する API の URL", "type": "string"}, "keyword": {"description": "シリーズ（パック）を表すキーワードのリスト", "items": {"type": "string"}, "type": "array"}, "logo": {"$ref": "#/components/schemas/ImageLogo"}, "name": {"description": "シリーズ（パック）名", "type": "string"}, "panorama": {"$ref": "#/components/schemas/ImagePanorama"}, "releasedEvent": {"$ref": "#/components/schemas/PublicationEvent"}, "sameAs": {"description": "シリーズ（パック）と同一内容のリンク", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "style": {"$ref": "#/components/schemas/Style"}, "type": {"description": "データの種別", "type": "string"}, "url": {"description": "シリーズ（パック）の情報を返す API の URL", "type": "string"}, "vertical": {"$ref": "#/components/schemas/ImageVertical"}, "vertical2x3": {"$ref": "#/components/schemas/ImageVertical2x3"}}, "required": ["type", "id", "name", "identifierGroup", "keyword", "description", "style", "actor", "contributor", "itemUrl"], "type": "object"}, "RadioSeriesList": {"additionalProperties": false, "description": "ラジオシリーズリスト情報", "properties": {"count": {"description": "ラジオシリーズの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "ラジオシリーズリスト", "items": {"$ref": "#/components/schemas/RadioSeries"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "RawBroadcastEventList": {"additionalProperties": false, "description": "複数のブロードキャストイベント情報です", "properties": {"result": {"description": "ブロードキャストイベント情報群", "items": {"$ref": "#/components/schemas/BroadcastEvent"}, "type": "array"}}, "required": ["result"], "type": "object"}, "ReleaseLevel": {"description": "再放送のレベル", "enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "RequestEnvironmentId": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "RequestOrder": {"enum": ["asc", "desc"], "type": "string"}, "SameAs": {"additionalProperties": false, "description": "同一内容のリンク", "properties": {"name": {"description": "同一内容のリンク名", "type": "string"}, "url": {"description": "同一内容のリンク URL", "type": "string"}}, "required": ["name"], "type": "object"}, "Search2Response": {"additionalProperties": false, "properties": {"result": {"$ref": "#/components/schemas/Search2Result"}, "resultUrl": {"type": "string"}}, "type": "object"}, "Search2Result": {"additionalProperties": false, "properties": {"nplaylist": {"$ref": "#/components/schemas/NPlaylistList"}, "radioepisode": {"$ref": "#/components/schemas/RadioEpisodeList"}, "radioseries": {"$ref": "#/components/schemas/RadioSeriesList"}, "tvepisode": {"$ref": "#/components/schemas/TVEpisodeList"}, "tvseries": {"$ref": "#/components/schemas/TVSeriesList"}}, "type": "object"}, "SeriesAdditionalProperty": {"additionalProperties": false, "description": "NHK で定義したシリーズのプロパティをまとめた情報", "properties": {"availableOnPlus": {"description": "NHKプラスで利用可能かを示すフラグ", "type": "boolean"}, "enableVariablePlayBackSpeedControl": {"description": "ビデオオブジェクトが可変の速度で再生速度できるかを示すフラグ", "type": "boolean"}, "episodeOrderBy": {"$ref": "#/components/schemas/EpisodeOrderBy"}, "layoutPattern": {"$ref": "#/components/schemas/LayoutPattern"}, "optional": {"description": "表示上ミュートしてよい取扱いとなっているものの情報", "items": {"type": "string"}, "type": "array"}, "publishLevel": {"$ref": "#/components/schemas/PublishLevel"}, "seriesPackStatus": {"$ref": "#/components/schemas/SeriesPackStatus"}, "supportMedia": {"description": "メディアのサポート対象", "items": {"type": "string"}, "type": "array"}, "supportMusicList": {"description": "楽曲情報の出力可否フラグ", "type": "boolean"}, "supportPlusEmbed": {"description": "プラス埋め込み可否フラグ", "type": "boolean"}}, "required": ["publishLevel", "layoutPattern", "episodeOrderBy", "availableOnPlus", "enableVariablePlayBackSpeedControl", "optional", "seriesPackStatus", "supportMedia", "supportMusicList", "supportPlusEmbed"], "type": "object"}, "SeriesIdentifierGroup": {"additionalProperties": false, "description": "シリーズに関連する ID をまとめた情報", "properties": {"aliasId": {"description": "エイリアス ID", "type": "string"}, "formatGenre": {"description": "シリーズのフォーマットジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "hashtag": {"description": "ハッシュタグ", "items": {"type": "string"}, "type": "array"}, "radioSeriesId": {"description": "ラジオシリーズ ID", "type": "string"}, "radioSeriesName": {"description": "ラジオシリーズ名", "type": "string"}, "radioSeriesPackId": {"description": "ラジオシリーズパック ID", "type": "string"}, "radioSeriesUId": {"description": "ラジオシリーズ UUID", "type": "string"}, "serviceId": {"description": "サービス ID", "items": {"$ref": "#/components/schemas/ServiceId"}, "type": "array"}, "siteId": {"description": "サイトID", "type": "string"}, "themeGenre": {"description": "シリーズのテーマジャンル", "items": {"$ref": "#/components/schemas/GenreItem"}, "type": "array"}, "tvSeriesId": {"description": "テレビシリーズ ID", "type": "string"}, "tvSeriesName": {"description": "テレビシリーズ名", "type": "string"}, "tvSeriesPackId": {"description": "テレビシリーズパック ID", "type": "string"}, "tvSeriesUId": {"description": "テレビシリーズ UUID", "type": "string"}}, "type": "object"}, "SeriesPackStatus": {"description": "CMS1 にて設定されるシリーズパックの状態", "enum": ["notPacked", "pack", "packed"], "type": "string"}, "SeriesSummary": {"additionalProperties": false, "description": "シリーズまたはシリーズパックの概要", "properties": {"additionalProperty": {"$ref": "#/components/schemas/SeriesAdditionalProperty"}, "canonical": {"description": "シリーズ（パック）のウェブページの URL", "type": "string"}, "description": {"description": "シリーズ（パック）を表すテキスト", "type": "string"}, "detailedCatch": {"description": "シリーズ（パック）のキャッチコピー", "type": "string"}, "detailedSeriesNameRuby": {"description": "シリーズ（パック）名のよみがな", "type": "string"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "hero": {"$ref": "#/components/schemas/ImageHero"}, "id": {"description": "シリーズ（パック）ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/SeriesIdentifierGroup"}, "itemUrl": {"description": "シリーズ（パック）のエピソードを取得する API の URL", "type": "string"}, "keyword": {"description": "シリーズ（パック）のキーワード", "items": {"type": "string"}, "type": "array"}, "logo": {"$ref": "#/components/schemas/ImageLogo"}, "name": {"description": "シリーズ（パック）名", "type": "string"}, "sameAs": {"description": "エピソードと同一内容の URL", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "style": {"$ref": "#/components/schemas/Style"}, "url": {"description": "シリーズ（パック）の情報を返す API の URL", "type": "string"}}, "required": ["id", "name", "url", "itemUrl", "keyword", "description", "identifierGroup"], "type": "object"}, "ServiceDetailed": {"additionalProperties": false, "description": "ブロードキャストサービスの情報とブロードキャストイベントの情報です", "properties": {"publication": {"description": "放送日時、エリアなどを含むブロードキャストイベントのリスト", "items": {"$ref": "#/components/schemas/BroadcastEvent"}, "type": "array"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService", "description": "ブロードキャストサービスの情報"}}, "required": ["publication"], "type": "object"}, "ServiceDetailedForNow": {"additionalProperties": false, "description": "直前、現在、次の3つのブロードキャストイベントの情報とサービスの情報です", "properties": {"following": {"$ref": "#/components/schemas/BroadcastEvent", "description": "次に放送予定のブロードキャストイベント"}, "present": {"$ref": "#/components/schemas/BroadcastEvent", "description": "現在放送中のブロードキャストイベント"}, "previous": {"$ref": "#/components/schemas/BroadcastEvent", "description": "直前放送のブロードキャストイベント"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService", "description": "ブロードキャストサービスの情報"}}, "type": "object"}, "ServiceDetailedForPF": {"additionalProperties": false, "description": "現在と次のブロードキャストイベントの情報とサービスの情報です", "properties": {"following": {"$ref": "#/components/schemas/BroadcastEvent", "description": "次に放送予定のブロードキャストイベント"}, "present": {"$ref": "#/components/schemas/BroadcastEvent", "description": "現在放送中のブロードキャストイベント"}, "publishedOn": {"$ref": "#/components/schemas/BroadcastService", "description": "ブロードキャストサービスの情報"}}, "type": "object"}, "ServiceId": {"description": "サービス ID", "enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s3", "s4", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "StreamType": {"description": "ビデオ・オーディオオブジェクトの配信形態", "enum": ["vod", "simul", "live", "clip"], "type": "string"}, "Style": {"additionalProperties": false, "description": "シリーズの背景・文字色を表す情報", "properties": {"linkDark": {"description": "暗いテーマのリンク文字色", "type": "string"}, "linkLight": {"description": "明るいテーマのリンク文字色", "type": "string"}, "primaryDark": {"description": "暗いテーマのメインカラー", "type": "string"}, "primaryLight": {"description": "明るいテーマのメインカラー", "type": "string"}, "textDark": {"description": "暗いテーマの文字色", "type": "string"}, "textLight": {"description": "明るいテーマの文字色", "type": "string"}}, "required": ["textLight", "textDark", "linkLight", "linkDark", "primaryLight", "primaryDark"], "type": "object"}, "SubjectOf": {"additionalProperties": false, "description": "ブロードキャストイベントに紐づく動画・音声配信 (エピソード ID が発番されない場合に値が入る)", "properties": {"audio": {"description": "オーディオオブジェクト", "items": {"$ref": "#/components/schemas/AudioObject"}, "type": "array"}, "video": {"description": "ビデオオブジェクト", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}}, "type": "object"}, "TVEpisode": {"additionalProperties": false, "description": "テレビエピソードの情報", "properties": {"actor": {"description": "エピソードで出演ありの出演者", "items": {"$ref": "#/components/schemas/Actor"}, "type": "array"}, "additionalProperty": {"$ref": "#/components/schemas/EpisodeAdditionalProperty"}, "associatedMedia": {"description": "エピソードの関連動画情報", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}, "canonical": {"description": "エピソードのウェブページの URL", "type": "string"}, "citation": {"description": "エピソードと関連するウェブページの URL", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "classic": {"$ref": "#/components/schemas/ImageClassic"}, "contributor": {"description": "エピソードで出演なしの関係者", "items": {"$ref": "#/components/schemas/Contributor"}, "type": "array"}, "dateCreated": {"description": "エピソードが生成された日時", "format": "date-time", "type": "string"}, "dateModified": {"description": "エピソードの CMS1 での更新日時", "format": "date-time", "type": "string"}, "description": {"description": "エピソードの説明", "type": "string"}, "detailedChapter": {"description": "エピソードのエピソード記事", "items": {"$ref": "#/components/schemas/DetailedChapter"}, "type": "array"}, "detailedComingEvent": {"$ref": "#/components/schemas/DetailedComingEvent"}, "detailedEpisodeNameRuby": {"description": "エピソード名のよみがな", "type": "string"}, "detailedMemo": {"description": "エピソードのメモ", "items": {"$ref": "#/components/schemas/DetailedMemo"}, "type": "array"}, "detailedRecentEvent": {"$ref": "#/components/schemas/DetailedRecentEvent"}, "episodeNumber": {"description": "エピソードの話数", "format": "int32", "type": "integer"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "eyecatchList": {"description": "エピソードのアイキャッチズ画像情報のリスト", "items": {"$ref": "#/components/schemas/ImageEyecatch"}, "type": "array"}, "id": {"description": "エピソード ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/EpisodeIdentifierGroup"}, "keyword": {"description": "エピソードのキーワード", "items": {"type": "string"}, "type": "array"}, "name": {"description": "エピソード名", "type": "string"}, "partOfSeries": {"$ref": "#/components/schemas/SeriesSummary"}, "releasedEvent": {"$ref": "#/components/schemas/PublicationEvent"}, "sameAs": {"description": "エピソードと同一内容の URL", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "type": {"description": "オブジェクトの種別", "type": "string"}, "url": {"description": "エピソードの情報を返す API の URL", "type": "string"}, "vertical2x3": {"$ref": "#/components/schemas/ImageVertical2x3"}, "video": {"description": "エピソードの本編動画情報", "items": {"$ref": "#/components/schemas/VideoObject"}, "type": "array"}}, "required": ["type", "id", "name", "identifierGroup", "keyword", "description", "actor", "contributor", "additionalProperty"], "type": "object"}, "TVEpisodeList": {"additionalProperties": false, "description": "テレビエピソードリスト情報", "properties": {"count": {"description": "テレビエピソードの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "テレビエピソードリスト", "items": {"$ref": "#/components/schemas/TVEpisode"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "TVSeries": {"additionalProperties": false, "description": "テレビシリーズ（パック）の情報", "properties": {"actor": {"description": "シリーズ（パック）で出演ありの出演者", "items": {"$ref": "#/components/schemas/Actor"}, "type": "array"}, "additionalProperty": {"$ref": "#/components/schemas/SeriesAdditionalProperty"}, "associatedMediaUrl": {"description": "シリーズに関連する動画情報", "items": {"type": "string"}, "type": "array"}, "canonical": {"description": "シリーズ（パック）のウェブページの URL", "type": "string"}, "citation": {"description": "シリーズ（パック）の関連リンク", "items": {"$ref": "#/components/schemas/Citation"}, "type": "array"}, "classic": {"$ref": "#/components/schemas/ImageClassic"}, "contributor": {"description": "シリーズ（パック）で出演なしの関係者", "items": {"$ref": "#/components/schemas/Contributor"}, "type": "array"}, "dateCreated": {"description": "シリーズ（パック）の作成日時", "format": "date-time", "type": "string"}, "dateModified": {"description": "シリーズ（パック）のいずれかプロパティの更新日時", "format": "date-time", "type": "string"}, "description": {"description": "シリーズ（パック）の説明", "type": "string"}, "detailedCatch": {"description": "シリーズ（パック）のキャッチコピー", "type": "string"}, "detailedMemo": {"description": "シリーズ（パック）のメモのリスト", "items": {"$ref": "#/components/schemas/DetailedMemo"}, "type": "array"}, "detailedSeriesNameRuby": {"description": "シリーズ（パック）名のよみがな", "type": "string"}, "detailedTimetable": {"description": "シリーズ（パック）のタイムテーブル", "items": {"$ref": "#/components/schemas/DetailedTimetable"}, "type": "array"}, "eyecatch": {"$ref": "#/components/schemas/ImageEyecatch"}, "hero": {"$ref": "#/components/schemas/ImageHero"}, "id": {"description": "シリーズ（パック）ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/SeriesIdentifierGroup"}, "itemUrl": {"description": "シリーズ（パック）のエピソードを取得する API の URL", "type": "string"}, "keyword": {"description": "シリーズ（パック）を表すキーワードのリスト", "items": {"type": "string"}, "type": "array"}, "logo": {"$ref": "#/components/schemas/ImageLogo"}, "name": {"description": "シリーズ（パック）名", "type": "string"}, "panorama": {"$ref": "#/components/schemas/ImagePanorama"}, "releasedEvent": {"$ref": "#/components/schemas/PublicationEvent"}, "sameAs": {"description": "シリーズ（パック）と同一内容のリンク", "items": {"$ref": "#/components/schemas/SameAs"}, "type": "array"}, "style": {"$ref": "#/components/schemas/Style"}, "type": {"description": "データの種別", "type": "string"}, "url": {"description": "シリーズ（パック）の情報を返す API の URL", "type": "string"}, "vertical": {"$ref": "#/components/schemas/ImageVertical"}, "vertical2x3": {"$ref": "#/components/schemas/ImageVertical2x3"}}, "required": ["type", "id", "name", "identifierGroup", "keyword", "description", "style", "actor", "contributor", "itemUrl"], "type": "object"}, "TVSeriesList": {"additionalProperties": false, "description": "テレビシリーズリスト情報", "properties": {"count": {"description": "テレビシリーズの総数", "format": "int32", "type": "integer"}, "nextUrl": {"description": "次のページの API URL", "type": "string"}, "prevUrl": {"description": "前のページの API URL", "type": "string"}, "result": {"description": "テレビシリーズリスト", "items": {"$ref": "#/components/schemas/TVSeries"}, "type": "array"}, "resultUrl": {"description": "現在のページの API URL", "type": "string"}}, "required": ["result", "count"], "type": "object"}, "TypeOfList": {"enum": ["series", "editorial", "program", "schedule", "search", "moment"], "type": "string"}, "VideoFormat": {"description": "ブロードキャストサービスの取り得る画像解像度のリスト", "enum": ["2K", "4K", "8K"], "type": "string"}, "VideoObject": {"additionalProperties": false, "description": "ビデオオブジェクトの情報", "properties": {"additionalProperty": {"$ref": "#/components/schemas/MediaAdditionalProperty"}, "description": {"description": "ビデオオブジェクトの説明 (本編の場合はブロードキャストイベントの description と同じ)", "type": "string"}, "detailedContent": {"description": "ビデオオブジェクトのストリーム情報のリスト", "items": {"$ref": "#/components/schemas/DetailedContent"}, "type": "array"}, "detailedContentStatus": {"$ref": "#/components/schemas/DetailedContentStatus"}, "detailedVideoDescriptor": {"description": "プラスのマニフェストファイルの情報", "type": "string"}, "duration": {"description": "ビデオオブジェクトの尺", "type": "string"}, "embedUrl": {"description": "ビデオオブジェクトの埋め込みプレイヤーの URL", "type": "string"}, "expires": {"description": "ビデオオブジェクトの視聴期限", "format": "date-time", "type": "string"}, "id": {"description": "ビデオオブジェクト ID", "type": "string"}, "identifierGroup": {"$ref": "#/components/schemas/VideoObjectIdentifierGroup"}, "name": {"description": "ビデオオブジェクトの名前 (本編の場合はブロードキャストイベントの name と同じ)", "type": "string"}, "publication": {"description": "ビデオオブジェクトの元となるブロードキャストイベント情報", "items": {"$ref": "#/components/schemas/BroadcastEventSummary"}, "type": "array"}, "regionsAllowed": {"description": "ビデオオブジェクトの配信地域制限 (JP:国内制限あり, パラメータなし:地域制限なし)", "type": "string"}, "sprite": {"$ref": "#/components/schemas/ImageSprite"}, "thumbnail": {"$ref": "#/components/schemas/ImageThumbnail", "description": "ビデオオブジェクトのサムネイル画像 URL"}, "uploadDate": {"description": "ビデオオブジェクトの公開日時", "type": "string"}, "url": {"description": "ビデオオブジェクトを再生できるウェブページの URL", "type": "string"}}, "required": ["id", "identifierGroup", "detailedContentStatus"], "type": "object"}, "VideoObjectIdentifierGroup": {"additionalProperties": false, "description": "ビデオオブジェクトに関連する ID をまとめた情報", "properties": {"bcAccountId": {"description": "brightcove 由来のアカウント ID", "type": "string"}, "bcVideoId": {"description": "brightcove 由来のビデオ ID", "type": "string"}, "broadcastEventId": {"description": "動画の元となるブロードキャストイベント ID", "type": "string"}, "environmentId": {"$ref": "#/components/schemas/EnvironmentId"}, "streamType": {"$ref": "#/components/schemas/StreamType"}}, "required": ["environmentId", "streamType"], "type": "object"}}}, "info": {"description": "各エンドポイントの Responses の Schema で ＊ マークが付いてるものは必ず出力されます<br>それ以外のものは nullable true です", "title": "番組 API -DEV-", "version": "1.0.0"}, "openapi": "3.0.1", "paths": {"/const.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/Const.html"}, "operationId": "GetConst", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Const"}}}, "description": "A successful response."}}, "summary": "定数 (area/service/genre/formatGenre/themeGenre/image) を取得", "tags": ["Const"]}}, "/f/broadcastevent/rs/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEventList.html"}, "operationId": "GetBroadcastEventFlattenWithRadioSeriesId", "parameters": [{"description": "Radio の seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "area", "schema": {"default": "None", "type": "string"}}, {"description": "取得する BroadcastEvent の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 100)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 100, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "BroadcastEvent を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "startDate", "enum": ["startDate"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "asc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に BroadcastEvent が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態でフィルタリングする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/EventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "eventShareStatus", "schema": {"default": "parent,multiple,single", "items": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "本放送・再放送の情報でフィルタリングする際の releaseLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/ReleaseLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "releaseLevel", "schema": {"default": "None", "items": {"enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "broadcastevent に紐付く episode が持つ利用可能な動画でフィルタリングする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEventList"}}}, "description": "A successful response."}}, "summary": "Radio の seriesId を指定して、series 下の各 episode に紐付く BroadcastEvent のリストを episodeId ごとに括らず同一階層で取得", "tags": ["Program"]}}, "/f/broadcastevent/ts/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEventList.html"}, "operationId": "GetBroadcastEventFlattenWithTVSeriesId", "parameters": [{"description": "TV の seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "area", "schema": {"default": "None", "type": "string"}}, {"description": "取得する BroadcastEvent の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 100)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 100, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "BroadcastEvent を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "startDate", "enum": ["startDate"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "asc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に BroadcastEvent が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態でフィルタリングする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/EventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "eventShareStatus", "schema": {"default": "parent,multiple,single", "items": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "本放送・再放送の情報でフィルタリングする際の releaseLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/ReleaseLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "releaseLevel", "schema": {"default": "None", "items": {"enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送に付随する手話や字幕データ放送の情報などでフィルタリングする際の support を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/support.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "support", "schema": {"default": "None", "items": {"enum": ["caption", "dataBroadcast", "sign", "interactive", "hybridcast"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "NHKプラスの配信状態でフィルタリングする際の playControl を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/playControl.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "playControl", "schema": {"default": "None", "items": {"enum": ["simul", "dvr", "vod"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "音声モードでフィルタリングする際の audioMode を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/AudioMode.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "audioMode", "schema": {"default": "None", "items": {"enum": ["lang2", "langN", "kaisetsu", "ch51", "ch222"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "broadcastevent に紐付く episode が持つ利用可能な動画でフィルタリングする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEventList"}}}, "description": "A successful response."}}, "summary": "TV の seriesId を指定して、series 下の各 episode に紐付く BroadcastEvent のリストを episodeId ごとに括らず同一階層で取得", "tags": ["Program"]}}, "/l/broadcastevent/re/{episodeId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEventList.html"}, "operationId": "GetBroadcastEventListWithRadioEpisodeId", "parameters": [{"description": "Radio の episodeId を指定", "in": "path", "name": "episodeId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "area", "schema": {"default": "None", "type": "string"}}, {"description": "取得する BroadcastEvent の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 100)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 100, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "BroadcastEvent を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "startDate", "enum": ["startDate"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "asc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に BroadcastEvent が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態でフィルタリングする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/EventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "eventShareStatus", "schema": {"default": "parent,multiple,single", "items": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "本放送・再放送の情報でフィルタリングする際の releaseLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/ReleaseLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "releaseLevel", "schema": {"default": "None", "items": {"enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "broadcastevent に紐付く episode が持つ利用可能な動画でフィルタリングする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEventList"}}}, "description": "A successful response."}}, "summary": "Radio の episodeId を指定して、episode に紐付く BroadcastEvent を取得", "tags": ["Program"]}}, "/l/broadcastevent/te/{episodeId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEventList.html"}, "operationId": "GetBroadcastEventListWithTVEpisodeId", "parameters": [{"description": "TV の episodeId を指定", "in": "path", "name": "episodeId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "area", "schema": {"default": "None", "type": "string"}}, {"description": "取得する BroadcastEvent の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 100)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 100, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "BroadcastEvent を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "startDate", "enum": ["startDate"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "asc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に BroadcastEvent が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態でフィルタリングする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/EventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "eventShareStatus", "schema": {"default": "parent,multiple,single", "items": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "本放送・再放送の情報でフィルタリングする際の releaseLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/ReleaseLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "releaseLevel", "schema": {"default": "None", "items": {"enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送に付随する手話や字幕データ放送の情報などでフィルタリングする際の support を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/support.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "support", "schema": {"default": "None", "items": {"enum": ["caption", "dataBroadcast", "sign", "interactive", "hybridcast"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "NHKプラスの配信状態でフィルタリングする際の playControl を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/playControl.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "playControl", "schema": {"default": "None", "items": {"enum": ["simul", "dvr", "vod"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "音声モードでフィルタリングする際の audioMode を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/AudioMode.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "audioMode", "schema": {"default": "None", "items": {"enum": ["lang2", "langN", "kaisetsu", "ch51", "ch222"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "broadcastevent に紐付く episode が持つ利用可能な動画でフィルタリングする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEventList"}}}, "description": "A successful response."}}, "summary": "TV の episodeId を指定して、episode に紐付く BroadcastEvent を取得", "tags": ["Program"]}}, "/l/radioepisode/rs/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/RadioEpisodeList.html"}, "operationId": "GetRadioEpisodeListWithSeriesId", "parameters": [{"description": "Radio の seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "取得する RadioEpisode の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 10)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 10, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのstartDateに対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのendDateに対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのendDateに対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのstartDateに対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "RadioEpisode を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "{Series}.additionalProperty.episodeOrderBy", "enum": ["episodeNumber", "releasedEvent", "recentEvent", "comingEvent", "dateModified"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "desc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に RadioEpisode が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"description": "放送ステータス（未放送または放送済）", "enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態で .audio[] 下をカットする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEventShareStatus", "schema": {"default": "parent,multiple,single,other", "items": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスで .audio[] 下をカットする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cService.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cService", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能な動画を持つ episode 内の .audio[] または .associatedMedia[] 下をカットする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEnvironmentId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEnvironmentId", "schema": {"default": "None", "items": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能なメタ (動画, 記事など) を持つ RadioEpisode でフィルタリングする際に、動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "エピソードに紐付く動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter 等の情報", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer", "detailedChapter"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RadioEpisodeList"}}}, "description": "A successful response."}}, "summary": "Radio の seriesId を指定して、RadioEpisode のリストを取得", "tags": ["Type-Episode"]}}, "/l/tvepisode/ts/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/TVEpisodeList.html"}, "operationId": "GetTVEpisodeListWithSeriesId", "parameters": [{"description": "TV の seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "取得する TVEpisode の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 10)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 10, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのstartDateに対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのendDateに対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのendDateに対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータのstartDateに対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "TVEpisode を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "{Series}.additionalProperty.episodeOrderBy", "enum": ["episodeNumber", "releasedEvent", "recentEvent", "comingEvent", "dateModified"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "desc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "放送ステータスでフィルタリングする際に TVEpisode が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"description": "放送ステータス（未放送または放送済）", "enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態で .video[] 下をカットする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEventShareStatus", "schema": {"default": "parent,multiple,single,other", "items": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスで .video[] 下をカットする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cService.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cService", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能な動画を持つ episode 内の .video[] または .associatedMedia[] 下をカットする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEnvironmentId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEnvironmentId", "schema": {"default": "None", "items": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能なメタ (動画, 記事など) を持つ TVEpisode でフィルタリングする際に、動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "エピソードに紐付く動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter 等の情報", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer", "detailedChapter"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TVEpisodeList"}}}, "description": "A successful response."}}, "summary": "TV の seriesId を指定して、TVEpisode のリストを取得", "tags": ["Type-Episode"]}}, "/pg/cp/be/{broadcastEventId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "指定した broadcastEventId を持つ番組の情報とその番組が放送されている時間に、親局で放送されている番組の情報を取得する。(放送時間が一部のみ被っている番組も含む。）<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/RawBroadcastEventList.html"}, "operationId": "GetCompetingProgram", "parameters": [{"description": "broadcastEventId を指定", "in": "path", "name": "broadcastEventId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RawBroadcastEventList"}}}, "description": "A successful response."}}, "summary": "broadcastEventId を指定して、親局で放送されている BroadcastEvent を取得", "tags": ["Program"]}}, "/pg/date/{service}/{area}/{date}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "publishedOn で, BroadcastService の情報を返す。放送日基準 (ICIS ベース)。<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/DateProgram.html"}, "operationId": "GetDateProgram", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3", "tv", "radio"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "130", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}, {"description": "日付を指定 (フォーマット: YYYY-MM-DD) (指定可能な日付の範囲は BroadcastEvent に準ずる。 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEvent.html\" target=\"_blank\">こちら</a>))", "in": "path", "name": "date", "required": true, "schema": {"type": "string"}}, {"description": "broadcastevent に紐付くepisode が持つ利用可能な動画でフィルタリングする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "availableOn", "schema": {"default": "None", "items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateProgram"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId、date を指定して、BroadcastEvent のリストを取得", "tags": ["Program"]}}, "/pg/genre/{service}/{area}/{genre}/{date}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "publishedOn で、放送サービスの情報を返す。放送日基準 (ICIS ベース)。<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/DateProgram.html"}, "operationId": "GetDateProgramWithGenre", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3", "tv", "radio"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}, {"description": "genreId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/genreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "path", "name": "genre", "required": true, "schema": {"default": "0000", "type": "string"}}, {"description": "日付を指定 (フォーマット: YYYY-MM-DD) (指定可能な日付の範囲は BroadcastEvent に準ずる。 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEvent.html\" target=\"_blank\">こちら</a>))", "in": "path", "name": "date", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateProgram"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId、genreId、date を指定して、BroadcastEvent のリストを取得", "tags": ["Program"]}}, "/pg/now/{service}/{area}/now.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "publishedOn で、放送サービスの情報を返す。<br>全地域・サービスに対応。流動には非対応。publishedOn で、放送サービスの情報を返す。<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/NowProgram.html"}, "operationId": "GetDateProgramNow", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3", "tv", "radio"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NowProgram"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId を指定して、previous/present/following の BroadcastEvent を取得", "tags": ["Program"]}}, "/pg/pf/{service}/{area}/pf.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "publishedOn で、放送サービスの情報を返す。<br>全地域・サービスに対応。東京の g1, g2, e1, e3, s1, s2, s3 の場合は流動に対応している。他は流動に対応しない(ラジオ含む)。<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/PFProgram.html"}, "operationId": "GetDateProgramPF", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3", "tv", "radio"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PFProgram"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId を指定して、present/following の BroadcastEvent を取得", "tags": ["Program"]}}, "/pg/week/{service}/{area}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "publishedOn で, BroadcastService の情報を返す。<br>レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/DateProgram.html"}, "operationId": "GetWeekProgram", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "130", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateProgram"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId を指定して、本日 〜 7 日先までの BroadcastEvent のリストを取得", "tags": ["Program"]}}, "/s/extended2.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/Search2Response.html"}, "operationId": "GetExtendedSearch2", "parameters": [{"description": "検索対象として tv または radio を指定", "example": "tv", "in": "query", "name": "modeOfItem", "required": true, "schema": {"enum": ["tv", "radio"], "type": "string"}}, {"description": "取得する nplaylist の typeOfList を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/TypeOfList.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "typeOfList", "schema": {"default": "None", "items": {"enum": ["series", "editorial", "program", "search"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "各 type でフィルタリングする際の type を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/Type.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "type", "schema": {"default": "series,episode,nplaylist", "items": {"enum": ["series", "episode", "nplaylist"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "各 type の name, description で検索する文字列を指定 (指定ルールは<a href=\"https://api-docs.nr.nhk.jp/r7/docs/column/lucene-parser.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "word", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "各 type の name, description で検索結果から除外する文字列を指定 (カンマ区切りで複数指定可)", "example": "", "in": "query", "name": "wordEx", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "各 type の actor.{person/organization}.name で検索する文字列を指定 (指定ルールは<a href=\"https://api-docs.nr.nhk.jp/r7/docs/column/lucene-parser.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "concern", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "各 type の actor.{person/organization}.name で検索結果から除外する文字列を指定 (カンマ区切りで複数指定可)", "example": "", "in": "query", "name": "concernEx", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "検索する人物・グループのNHK人物IDを指定 (カンマ区切りで複数指定可)", "explode": false, "in": "query", "name": "npgId", "schema": {"items": {"example": "", "type": "string"}, "maxItems": 50, "type": "array"}, "style": "form"}, {"description": "同一人物情報を取得する際に true を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/aka.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "aka", "schema": {"default": "None", "enum": ["true"], "type": "boolean"}}, {"description": "各 type の keyword で検索する文字列を指定 (指定ルールは<a href=\"https://api-docs.nr.nhk.jp/r7/docs/column/lucene-parser.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "keyword", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "各 type の keyword で検索結果から除外する文字列を指定 (カンマ区切りで複数指定可)", "example": "", "in": "query", "name": "keywordEx", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "tvseries/radioseries/nplaylist のフォーマットジャンルでフィルタリングする際の formatGenreId を指定 (type=series,nplaylist いずれかの指定が必須、type=episode が含まれる場合 400) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/formatGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "formatGenreId", "schema": {"default": "None", "items": {"enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "tvseries/radioseries/nplaylist のフォーマットジャンルで検索結果から除外する formatGenreId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/formatGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "formatGenreIdEx", "schema": {"default": "None", "items": {"enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"], "type": "string"}, "type": "string"}}, {"description": "tvseries/radioseries/nplaylist のテーマジャンルでフィルタリングする際の themeGenreId を指定 (type=series,nplaylist いずれかの指定が必須、type=episode が含まれる場合 400) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/themeGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "themeGenreId", "schema": {"default": "None", "type": "string"}}, {"description": "tvseries/radioseries/nplaylist のテーマジャンルで検索結果から除外する themeGenreId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/themeGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "themeGenreIdEx", "schema": {"default": "None", "type": "string"}}, {"description": "tvepisode/radioepisode のフォーマットジャンルでフィルタリングする際の formatGenreId を指定 (type=episode の指定が必須、type=series,nplaylist いずれかが含まれる場合 400) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/formatGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "formatGenreTagId", "schema": {"default": "None", "items": {"enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "tvepisode/radioepisode のフォーマットジャンルで検索結果から除外する formatGenreId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/formatGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "formatGenreTagIdEx", "schema": {"default": "None", "items": {"enum": ["00", "01", "02", "03", "04", "05", "06", "07", "08", "09"], "type": "string"}, "type": "string"}}, {"description": "tvepisode/radioepisode のテーマジャンルでフィルタリングする際の themeGenreId を指定 (type=episode の指定が必須、type=series,nplaylist いずれかが含まれる場合 400) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/themeGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "themeGenreTagId", "schema": {"default": "None", "type": "string"}}, {"description": "tvepisode/radioepisode のフォーマットジャンルで検索結果から除外する formatGenreId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/themeGenreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "themeGenreTagIdEx", "schema": {"default": "None", "type": "string"}}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s3", "s4", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "playlist の公開レベル(5段階)でフィルタリングする際の publishLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/PublishLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "publishLevel", "schema": {"default": "ready,full,limited", "items": {"enum": ["notyet", "ready", "full", "limited", "gone"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "OpenSearch で算出される score が指定値以上の type にフィルタリングする際の score を指定", "in": "query", "name": "minScore", "schema": {"minimum": 0, "type": "number"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (releasedEvent/recentEvent においては startDate 基準) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "desc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "type を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "score", "description": "検索結果のタイプを並び替えるパラメータ", "enum": ["score", "dateModified", "releasedEvent", "recentEvent", "comingEvent", "episodeNumber"], "type": "string"}}, {"description": "曖昧検索をする場合、true を指定", "example": "", "in": "query", "name": "isFuzzy", "schema": {"default": "false", "enum": ["true", "false"], "type": "boolean"}}, {"description": "取得する type の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 10)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 10, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "series のパック状態でフィルタリングする際の seriesPackStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/SeriesPackStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "seriesPackStatus", "schema": {"default": "type が series または nplaylist の場合は pack, notPacked", "items": {"enum": ["pack", "packed", "notPacked"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "検索対象とする seriesId を指定 (カンマ区切りで複数指定可) (上限: 50)", "explode": false, "in": "query", "name": "seriesId", "schema": {"items": {"example": "", "pattern": "^[0-9A-Z]+$", "type": "string"}, "maxItems": 100, "type": "array"}, "style": "form"}, {"description": "検索結果から除外する seriesId を指定 (カンマ区切りで複数指定可) (上限: 50)", "explode": false, "in": "query", "name": "seriesIdEx", "schema": {"items": {"example": "", "pattern": "^[0-9A-Z]+$", "type": "string"}, "maxItems": 100, "type": "array"}, "style": "form"}, {"description": "検索対象とする episodeId を指定 (カンマ区切りで複数指定可) (上限: 100)", "explode": false, "in": "query", "name": "episodeId", "schema": {"items": {"example": "", "pattern": "^[0-9A-Z]+$", "type": "string"}, "maxItems": 100, "type": "array"}, "style": "form"}, {"description": "検索対象から除外する episodeId を指定 (カンマ区切りで複数指定可) (上限: 50)", "explode": false, "in": "query", "name": "episodeIdEx", "schema": {"items": {"example": "", "pattern": "^[0-9A-Z]+$", "type": "string"}, "maxItems": 100, "type": "array"}, "style": "form"}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の始点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "from", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの日時に対してフィルタリングする際の終点となる日時を指定 (releasedEvent/recentEvent/comingEvent においては startDate 基準) (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "to", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの startDate に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの endDate に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの endDate に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "orderBy で指定したパラメータの startDate に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "from/to で指定した日時の対象となるパラメータを指定 (orderBy=score,episodeNumber 指定時に使用) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/dateFilterBy.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "dateFilter<PERSON>y", "schema": {"description": "エピソードを並び替えるパラメータ", "enum": ["dateModified", "releasedEvent", "recentEvent", "comingEvent"], "type": "string"}}, {"description": "利用可能なメタ (動画, 記事など) を持つ episode でフィルタリングする際に、動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter を指定 (type=episode 指定必須、type=series,nplaylist いずれかが含まれる場合 400) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "explode": false, "in": "query", "name": "availableOn", "schema": {"items": {"description": "エピソードに紐付く動画の配信環境を表す environmentId やエピソード記事を表す detailedChapter 等の情報", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer", "detailedChapter"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送ステータスでフィルタリングする際に episode が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"description": "放送ステータス（未放送または放送済）", "enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "", "example": "", "in": "query", "name": "searchScope", "schema": {"default": "None", "enum": ["simple", "default", "all"], "type": "string"}}, {"description": "編成状態で .video[] 下をカットする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEventShareStatus", "schema": {"default": "parent,multiple,single,other", "items": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスで .video[] または .audio[] 下をカットする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cService.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cService", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能な動画を持つ episode 内の .video[] または .associatedMedia[] 下をカットする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEnvironmentId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEnvironmentId", "schema": {"default": "None", "items": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Search2Response"}}}, "description": "A successful response."}}, "summary": "クエリで指定した条件に当てはまる series, episode, nplaylist のリストを取得", "tags": ["Search2"]}}, "/s/program2.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEventList.html"}, "operationId": "GetProgramSearch2", "parameters": [{"description": "BroadcastEvent の name, description で検索する文字列を指定 (指定ルールは<a href=\"https://api-docs.nr.nhk.jp/r7/docs/column/lucene-parser.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "word", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "BroadcastEvent の name, description で検索結果から除外する文字列を指定 (カンマ区切りで複数指定可)", "example": "", "in": "query", "name": "wordEx", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "BroadcastEvent の .misc.actList[].name で検索する文字列を指定 (指定ルールは<a href=\"https://api-docs.nr.nhk.jp/r7/docs/column/lucene-parser.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "concern", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "BroadcastEvent の .misc.actList[].name で検索結果から除外する文字列を指定 (カンマ区切りで複数指定可)", "example": "", "in": "query", "name": "concernEx", "schema": {"default": "None", "maxLength": 100, "type": "string"}}, {"description": "放送エリアでフィルタリングする際の areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "example": "130", "in": "query", "name": "area", "required": true, "schema": {"type": "string"}}, {"description": "放送サービスでフィルタリングする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "service", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "BroadcastEvent のジャンルでフィルタリングする際の genreCode を指定 (カンマ区切りで複数指定可) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/genreId.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "genreCode", "schema": {"default": "None", "type": "string"}}, {"description": "", "example": "", "in": "query", "name": "fromDate", "schema": {"default": "None", "format": "date", "type": "string"}}, {"description": "", "example": "", "in": "query", "name": "toDate", "schema": {"default": "None", "format": "date", "type": "string"}}, {"description": "startDate に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "endDate に対してフィルタリングする際の終点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "toEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "endDate に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromEndDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "startDate に対してフィルタリングする際の始点となる日時を指定 (フォーマット: YYYY-MM-DD または YYYY-MM-DDThh:mm:ss%2B09:00, %2B09:00は省略可能, 秒は切り捨て) (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/from-to.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "fromStartDate", "schema": {"default": "None", "format": "date-time", "type": "string"}}, {"description": "BroadcastEvent を並び替えるパラメータを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "orderBy", "schema": {"default": "score", "enum": ["score", "startDate"], "type": "string"}}, {"description": "orderBy で指定したパラメータによる並び順の昇順 (asc)・降順 (desc) を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/orderBy.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "order", "schema": {"default": "desc", "enum": ["asc", "desc"], "type": "string"}}, {"description": "曖昧検索をする場合、true を指定", "example": "", "in": "query", "name": "isFuzzy", "schema": {"default": "false", "enum": ["true", "false"], "type": "boolean"}}, {"description": "放送ステータスでフィルタリングする際に BroadcastEvent が未放送または放送済みを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/status.html\" target=\"_blank\">こちら</a>)", "in": "query", "name": "status", "schema": {"enum": ["broadcasted", "scheduled"], "type": "string"}}, {"description": "編成状態でフィルタリングする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/EventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "eventShareStatus", "schema": {"default": "parent,multiple,single", "items": {"description": "ブロードキャストイベントのメイン・サブチャンネルの編成状態", "enum": ["parent", "child", "multiple", "single"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "本放送・再放送の情報でフィルタリングする際の releaseLevel を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/ReleaseLevel.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "releaseLevel", "schema": {"default": "None", "items": {"enum": ["original", "prime", "normal", "repeat"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送に付随する手話や字幕データ放送の情報などでフィルタリングする際の support を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/support.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "support", "schema": {"default": "None", "items": {"enum": ["caption", "dataBroadcast", "sign", "interactive", "hybridcast"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "NHKプラスの配信状態でフィルタリングする際の playControl を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/playControl.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "playControl", "schema": {"default": "None", "items": {"enum": ["simul", "dvr", "vod"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "音声モードでフィルタリングする際の audioMode を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/AudioMode.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "audioMode", "schema": {"default": "None", "items": {"enum": ["lang2", "langN", "kaisetsu", "ch51", "ch222"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送枠の長さ (秒) を指定値以上の BroadcastEvent のみにフィルタリングする際の放送枠の長さ (秒) を指定", "example": "", "in": "query", "name": "minDuration", "schema": {"default": "None", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "放送枠の長さ (秒) を指定値未満の BroadcastEvent のみにフィルタリングする際の放送枠の長さ (秒) を指定", "example": "", "in": "query", "name": "maxDuration", "schema": {"default": "None", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "BroadcastEvent の 放送範囲でフィルタリングする際の Coverage を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/enum/Coverage.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "coverage", "schema": {"default": "None", "items": {"enum": ["local", "block", "nationwide"], "type": "string"}, "type": "array"}}, {"description": "取得する BroadcastEvent の数を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/size.html\" target=\"_blank\">こちら</a>) (上限: 12)", "example": "", "in": "query", "name": "size", "schema": {"default": "10", "format": "int32", "maximum": 12, "minimum": 1, "type": "integer"}}, {"description": "リストの先頭をデフォルトから何件シフトするかを指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/offset.html\" target=\"_blank\">こちら</a>)", "example": "", "in": "query", "name": "offset", "schema": {"default": "0", "format": "int32", "minimum": 0, "type": "integer"}}, {"description": "OpenSearch で算出される score が指定値以上の BroadcastEvent にフィルタリングする際の score を指定", "in": "query", "name": "minScore", "schema": {"minimum": 0, "type": "number"}}, {"description": "BroadcastEvent に紐付く episode が持つ利用可能な動画でフィルタリングをする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/availableOn.html\" target=\"_blank\">こちら</a>)", "explode": false, "in": "query", "name": "availableOn", "schema": {"items": {"description": "ブロードキャストイベントに紐付く動画の配信環境を表す environmentId 情報", "enum": ["hskOriginal", "radiruOriginal"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "", "example": "", "in": "query", "name": "searchScope", "schema": {"default": "None", "enum": ["default", "detailed", "all"], "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEventList"}}}, "description": "A successful response."}}, "summary": "クエリで指定した条件に当てはまる BroadcastEvent のリストを取得", "tags": ["Search2"]}}, "/t/broadcastevent/be/{broadcastEventId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastEvent.html"}, "operationId": "GetBroadcastEvent", "parameters": [{"description": "broadcastEventId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/broadcastEventId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "broadcastEventId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastEvent"}}}, "description": "A successful response."}}, "summary": "broadcastEventId を指定して、BroadcastEvent を取得", "tags": ["Program"]}}, "/t/broadcastservice/bs/{service}-{area}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/BroadcastService.html"}, "operationId": "GetBroadcastServiceWithArea", "parameters": [{"description": "serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/serviceId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "service", "required": true, "schema": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}}, {"description": "areaId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/id/areaId.html\" target=\"_blank\">こちら</a>)", "in": "path", "name": "area", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastService"}}}, "description": "A successful response."}}, "summary": "serviceId、areaId を指定して、BroadcastService を取得", "tags": ["<PERSON><PERSON>"]}}, "/t/radioepisode/re/{episodeId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/RadioEpisode.html"}, "operationId": "GetRadioEpisode", "parameters": [{"description": "Radio の episodeId を指定", "in": "path", "name": "episodeId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "編成状態で .audio[] 下をカットする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEventShareStatus", "schema": {"default": "parent,multiple,single,other", "items": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスで .audio[] 下をカットする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cService.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cService", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能な動画を持つ episode 内の .audio[] または .associatedMedia[] 下をカットする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEnvironmentId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEnvironmentId", "schema": {"default": "None", "items": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RadioEpisode"}}}, "description": "A successful response."}}, "summary": "Radio の episodeId を指定して、RadioEpisode を取得", "tags": ["Type-Episode"]}}, "/t/radioseries/rs/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/RadioSeries.html"}, "operationId": "GetRadioSeries", "parameters": [{"description": "seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RadioSeries"}}}, "description": "A successful response."}}, "summary": "Radio の seriesId を指定して、RadioSeries を取得", "tags": ["Type-Series"]}}, "/t/tvepisode/te/{episodeId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/TVEpisode.html"}, "operationId": "GetTVEpisode", "parameters": [{"description": "TV の episodeId を指定", "in": "path", "name": "episodeId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}, {"description": "編成状態で .video[] 下をカットする際の eventShareStatus を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEventShareStatus.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEventShareStatus", "schema": {"default": "parent,multiple,single,other", "items": {"description": "ブロードキャストイベントの編成状態", "enum": ["parent", "child", "multiple", "single", "other"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "放送サービスで .video[] 下をカットする際の serviceId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cService.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cService", "schema": {"default": "None", "items": {"enum": ["g1", "g2", "e1", "e3", "s1", "s2", "s5", "s6", "r1", "r2", "r3"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "利用可能な動画を持つ episode 内の .video[] または .associatedMedia[] 下をカットする際に、動画の配信環境を表す environmentId を指定 (詳細は<a href=\"https://api-docs.nr.nhk.jp/r7/docs/query/cEnvironmentId.html\" target=\"_blank\">こちら</a>)", "example": "", "explode": false, "in": "query", "name": "cEnvironmentId", "schema": {"default": "None", "items": {"description": "動画配信環境の ID", "enum": ["hskOriginal", "radiruOriginal", "bcUploadTrailer"], "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TVEpisode"}}}, "description": "A successful response."}}, "summary": "TV の episodeId を指定して、TVEpisode を取得", "tags": ["Type-Episode"]}}, "/t/tvseries/ts/{seriesId}.json": {"get": {"deprecated": false, "description": "", "externalDocs": {"description": "レスポンス詳細", "url": "https://api-docs.nr.nhk.jp/r7/docs/responseObject/TVSeries.html"}, "operationId": "GetTVSeries", "parameters": [{"description": "seriesId を指定", "in": "path", "name": "seriesId", "required": true, "schema": {"pattern": "^[0-9A-Z]+$", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TVSeries"}}}, "description": "A successful response."}}, "summary": "TV の seriesId を指定して、TVSeries を取得", "tags": ["Type-Series"]}}}, "servers": [{"description": "", "url": "https://api.nhk.jp/r7"}, {"description": "", "url": "https://stg-api.nhk.jp/r7"}, {"description": "", "url": "https://dev-api.nhk.jp/r7"}]}