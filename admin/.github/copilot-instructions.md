# NHK-Counter Admin Copilot Instructions

## Project Overview

NHK-Counter Admin is a Next.js-based management panel for NHK's voting and messaging system. It provides an interface for managing programs, real-time counters, message topics, and aggregation API access tokens. The project is part of a larger microservice-based system that handles high-volume voting and messaging for NHK broadcasting programs.

## Language

You can think in English but when you talk to user and write document, use Japanese.

## Technology Stack

- **Framework**: Next.js with App Router
- **UI Components**: shadcn/ui (based on Radix UI)
- **Styling**: Tailwind CSS
- **State Management**: React Context and Server Components
- **Form Validation**: Zod schema validation
- **Authentication**: NextAuth.js with Keycloak integration
- **Testing**: Vitest with TestContainers for isolated DynamoDB tests
- **Data Storage**: AWS DynamoDB
- **External APIs**: NHK R7 API for program information

## Project Structure

- `/src/app`: Next.js App Router structure
  - `/(after-login)`: Routes that require authentication
  - `/(before-login)`: Public routes and authentication pages
- `/src/adapter`: External service integrations
  - `/dynamodb`: DynamoDB entity-specific modules
  - `/lambda`: Lambda function interactions
- `/src/components`: Shared UI components
  - `/ui`: shadcn/ui components
  - `/server-scale`: Server scaling components
- `/src/lib`: Utility functions and constants
- `/src/utils`: Helper functions and hooks
- `/src/r7`: NHK R7 API client

## Code Style and Patterns

When contributing to this project, follow these guidelines:

1. **File Naming**:
   - Use kebab-case for general files
   - Use PascalCase for React components and TypeScript interfaces
   - Use `.tsx` extension for React components and `.ts` for utilities

2. **Import Order**:
   - React and Next.js imports first
   - External libraries next
   - Internal components and utilities
   - Styles last

3. **Component Structure**:
   - Server components by default, client components only when necessary
   - Use "use client" directive at the top of client components
   - Keep components focused and small
   - Extract reusable logic to custom hooks in `/src/utils/hooks`

4. **Data Fetching**:
   - Use server components for data fetching when possible
   - Follow the adapter pattern for external service integration
   - Implement proper error handling for all API calls

5. **UI Patterns**:
   - Follow shadcn/ui design patterns for consistency
   - Use Tailwind CSS for styling
   - Implement responsive designs with mobile-first approach
   - Ensure proper accessibility (ARIA attributes, keyboard navigation)

6. **Authentication and Authorization**:
   - Use proper role-based access control
   - Implement program-specific permissions
   - Validate access rights in server actions

## Key Implementation Paths

### Program Management
- Program creation with unique IDs
- Association with NHK series data
- Setting scaling parameters
- Permission management

### Real-time Counters
- Vote topic management
- Real-time result viewing
- Group-based vote organization

### Message Management
- Message topic creation
- Real-time message streaming
- Message search and filtering

### Authentication Flow
- NextAuth.js for user authentication
- Role-based access control
- Program-specific permissions
- Session management

## Common Challenges and Known Issues

1. **API Integration**:
   - NHK R7 API requires specific request formats
   - Handle API failures gracefully
   - Generate types from OpenAPI specification with `npm run codegen`

2. **Scaling Settings**:
   - Balance resource allocation with expected traffic
   - Set appropriate margin times for broadcasts
   - Monitor during high-traffic events

3. **Permission Management**:
   - Complex permission model with program-specific access
   - Role-based permissions at both global and program levels
   - Ensure proper access control throughout the application

## Development Workflow

1. **Local Development**:
   - Run `npm run dev` to start the development server on port 8070
   - Run `npm run codegen` to generate OpenAPI types
   - Use environment flags for development-specific behavior

2. **Testing**:
   - Write unit and integration tests with Vitest
   - Use TestContainers for isolated DynamoDB testing
   - Use mock utilities for time-dependent tests

3. **Deployment**:
   - Follow Next.js build process
   - Use Docker containers for deployment
   - Configure via environment variables

## Best Practices

1. **Error Handling**:
   - Implement proper try/catch blocks
   - Display user-friendly error messages
   - Log errors for debugging

2. **Performance**:
   - Optimize component rendering
   - Implement proper data fetching strategies
   - Use pagination for large data sets

3. **Security**:
   - Validate all input data
   - Implement proper access control
   - Use CSRF protection

4. **Accessibility**:
   - Follow WCAG guidelines
   - Ensure keyboard navigation
   - Provide proper alternative text for images

Remember to always consider the end-users of this administrative panel and prioritize usability and efficiency in your implementations.
